<?php
session_start();
require_once 'config/database.php';

// التحقق من وجود slug المنتج
if (!isset($_GET['slug']) || empty($_GET['slug'])) {
    header('Location: products.php');
    exit();
}

$slug = sanitize_input($_GET['slug']);

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // التحقق من وجود عمود video_file
    $video_file_exists = false;
    try {
        $check_column = $conn->query("SHOW COLUMNS FROM products LIKE 'video_file'");
        $video_file_exists = $check_column->rowCount() > 0;
    } catch (Exception $e) {
        $video_file_exists = false;
    }

    // جلب بيانات المنتج
    $video_select = $video_file_exists ? ", p.video_file" : "";
    $product_query = "SELECT p.*, c.name as category_name, c.slug as category_slug $video_select
                      FROM products p
                      LEFT JOIN categories c ON p.category_id = c.id
                      WHERE p.slug = :slug AND p.is_active = 1";
    
    $product_stmt = $conn->prepare($product_query);
    $product_stmt->bindParam(':slug', $slug);
    $product_stmt->execute();
    
    $product = $product_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        header('Location: products.php');
        exit();
    }
    
    // تحديث عدد المشاهدات
    $update_views = "UPDATE products SET views_count = views_count + 1 WHERE id = :product_id";
    $update_stmt = $conn->prepare($update_views);
    $update_stmt->bindParam(':product_id', $product['id']);
    $update_stmt->execute();
    
    // جلب ألوان المنتج
    $colors_query = "SELECT c.* FROM colors c 
                     INNER JOIN product_colors pc ON c.id = pc.color_id 
                     WHERE pc.product_id = :product_id AND c.is_active = 1 
                     ORDER BY c.name";
    
    $colors_stmt = $conn->prepare($colors_query);
    $colors_stmt->bindParam(':product_id', $product['id']);
    $colors_stmt->execute();
    $colors = $colors_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب مقاسات المنتج
    $sizes_query = "SELECT s.* FROM sizes s 
                    INNER JOIN product_sizes ps ON s.id = ps.size_id 
                    WHERE ps.product_id = :product_id AND s.is_active = 1 
                    ORDER BY s.sort_order";
    
    $sizes_stmt = $conn->prepare($sizes_query);
    $sizes_stmt->bindParam(':product_id', $product['id']);
    $sizes_stmt->execute();
    $sizes = $sizes_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب صور المنتج
    $images_query = "SELECT * FROM product_images 
                     WHERE product_id = :product_id 
                     ORDER BY sort_order, id";
    
    $images_stmt = $conn->prepare($images_query);
    $images_stmt->bindParam(':product_id', $product['id']);
    $images_stmt->execute();
    $images = $images_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب منتجات مشابهة
    $related_query = "SELECT * FROM products 
                      WHERE category_id = :category_id 
                      AND id != :product_id 
                      AND is_active = 1 
                      ORDER BY RAND() 
                      LIMIT 4";
    
    $related_stmt = $conn->prepare($related_query);
    $related_stmt->bindParam(':category_id', $product['category_id']);
    $related_stmt->bindParam(':product_id', $product['id']);
    $related_stmt->execute();
    $related_products = $related_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إعداد عنوان الصفحة
    $page_title = $product['title'];
    $page_description = substr(strip_tags($product['description']), 0, 160);
    
} catch (Exception $e) {
    error_log("خطأ في جلب بيانات المنتج: " . $e->getMessage());
    header('Location: products.php');
    exit();
}

include 'includes/header.php';
?>

<div class="product-page">
    <div class="container">
        
        <!-- Breadcrumb -->
        <nav class="breadcrumb">
            <a href="index.php">الرئيسية</a>
            <span>/</span>
            <a href="products.php">المنتجات</a>
            <span>/</span>
            <?php if ($product['category_name']): ?>
                <a href="products.php?category=<?php echo $product['category_slug']; ?>"><?php echo $product['category_name']; ?></a>
                <span>/</span>
            <?php endif; ?>
            <span class="current"><?php echo $product['title']; ?></span>
        </nav>

        <div class="product-content">
            
            <!-- Product Images -->
            <div class="product-images">
                <div class="main-image">
                    <?php if ($product['featured_image']): ?>
                        <img src="uploads/<?php echo $product['featured_image']; ?>" 
                             alt="<?php echo $product['title']; ?>" 
                             id="mainProductImage">
                    <?php else: ?>
                        <div class="no-image">
                            <i class="fas fa-image"></i>
                            <span>لا توجد صورة</span>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if (!empty($images)): ?>
                <div class="thumbnail-images">
                    <?php
                    $all_images = [];

                    // إضافة الصورة المميزة أولاً إذا كانت موجودة
                    if ($product['featured_image']) {
                        $all_images[] = [
                            'image_path' => $product['featured_image'],
                            'alt_text' => $product['title'],
                            'is_featured' => true
                        ];
                    }

                    // إضافة باقي الصور (تجنب تكرار الصورة المميزة)
                    foreach ($images as $image) {
                        if ($image['image_path'] !== $product['featured_image']) {
                            $all_images[] = [
                                'image_path' => $image['image_path'],
                                'alt_text' => $image['alt_text'] ?: $product['title'],
                                'is_featured' => false
                            ];
                        }
                    }

                    // عرض جميع الصور
                    foreach ($all_images as $index => $image): ?>
                        <img src="uploads/<?php echo $image['image_path']; ?>"
                             alt="<?php echo $image['alt_text']; ?>"
                             class="thumbnail <?php echo $index === 0 ? 'active' : ''; ?>"
                             onclick="changeMainImage(this.src)">
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>

            <!-- Product Details -->
            <div class="product-details">
                <h1 class="product-title"><?php echo $product['title']; ?></h1>

                <!-- Lifestyle Images Dropdown -->
                <?php
                // جلب صور الطبيعة
                try {
                    $lifestyle_query = "SELECT * FROM product_images WHERE product_id = :product_id AND image_type = 'lifestyle' ORDER BY sort_order";
                    $lifestyle_stmt = $conn->prepare($lifestyle_query);
                    $lifestyle_stmt->bindParam(':product_id', $product['id']);
                    $lifestyle_stmt->execute();
                    $lifestyle_images = $lifestyle_stmt->fetchAll(PDO::FETCH_ASSOC);
                } catch (Exception $e) {
                    $lifestyle_images = [];
                }
                ?>

                <?php if (!empty($lifestyle_images)): ?>
                <div class="lifestyle-dropdown-title">
                    <button class="lifestyle-toggle" onclick="toggleLifestyleImages()">
                        <i class="fas fa-camera"></i>
                        <span>صور على الطبيعة (<?php echo count($lifestyle_images); ?>)</span>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </button>

                    <div class="lifestyle-content" id="lifestyleContent">
                        <div class="lifestyle-grid">
                            <?php foreach ($lifestyle_images as $image): ?>
                                <div class="lifestyle-thumb">
                                    <img src="uploads/<?php echo htmlspecialchars($image['image_path']); ?>"
                                         alt="<?php echo htmlspecialchars($image['alt_text']); ?>"
                                         onclick="openImageModal(this.src, '<?php echo htmlspecialchars($image['alt_text']); ?>')">
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Product Video Section -->
                <?php
                $has_video_file = $video_file_exists && !empty($product['video_file']);
                $has_video_url = !empty($product['video_url']);
                if ($has_video_url || $has_video_file):
                ?>
                <div class="product-video-section">
                    <button class="video-toggle" onclick="toggleProductVideo()">
                        <i class="fas fa-play-circle"></i>
                        <span>مشاهدة الفيديو</span>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </button>

                    <div class="video-content" id="videoContent">
                        <div class="video-wrapper">
                            <?php if ($has_video_file): ?>
                                <!-- فيديو مرفوع -->
                                <video controls preload="metadata">
                                    <source src="uploads/<?php echo htmlspecialchars($product['video_file']); ?>" type="video/mp4">
                                    متصفحك لا يدعم عرض الفيديو.
                                </video>
                            <?php elseif ($has_video_url): ?>
                                <!-- فيديو من رابط خارجي -->
                                <?php
                                $video_url = $product['video_url'];
                                $embed_url = '';

                                // تحويل رابط YouTube إلى embed
                                if (strpos($video_url, 'youtube.com/watch?v=') !== false) {
                                    $video_id = substr($video_url, strpos($video_url, 'v=') + 2);
                                    $video_id = substr($video_id, 0, strpos($video_id, '&') ?: strlen($video_id));
                                    $embed_url = "https://www.youtube.com/embed/$video_id";
                                } elseif (strpos($video_url, 'youtu.be/') !== false) {
                                    $video_id = substr($video_url, strpos($video_url, 'youtu.be/') + 9);
                                    $embed_url = "https://www.youtube.com/embed/$video_id";
                                } elseif (strpos($video_url, 'vimeo.com/') !== false) {
                                    $video_id = substr($video_url, strpos($video_url, 'vimeo.com/') + 10);
                                    $embed_url = "https://player.vimeo.com/video/$video_id";
                                } else {
                                    $embed_url = $video_url;
                                }
                                ?>

                                <iframe src="<?php echo htmlspecialchars($embed_url); ?>"
                                        frameborder="0"
                                        allowfullscreen
                                        loading="lazy">
                                </iframe>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="product-price">
                    <?php if ($product['sale_price'] && $product['sale_price'] > 0): ?>
                        <span class="sale-price"><?php echo format_price($product['sale_price']); ?></span>
                        <span class="original-price"><?php echo format_price($product['price']); ?></span>
                        <span class="discount-badge">
                            خصم <?php echo round((($product['price'] - $product['sale_price']) / $product['price']) * 100); ?>%
                        </span>
                    <?php else: ?>
                        <span class="current-price"><?php echo format_price($product['price']); ?></span>
                    <?php endif; ?>
                </div>

                <div class="product-description">
                    <p><?php echo nl2br($product['description']); ?></p>
                </div>

                <!-- Product Options Form -->
                <form id="addToCartForm" class="product-options">
                    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                    
                    <!-- Colors -->
                    <?php if (!empty($colors)): ?>
                    <div class="option-group">
                        <label class="option-label">اللون:</label>
                        <div class="color-options">
                            <?php foreach ($colors as $index => $color): ?>
                                <label class="color-option">
                                    <input type="radio" name="color_id" value="<?php echo $color['id']; ?>" 
                                           <?php echo $index === 0 ? 'checked' : ''; ?> required>
                                    <span class="color-swatch" 
                                          style="background-color: <?php echo $color['hex_code']; ?>"
                                          title="<?php echo $color['name']; ?>"></span>
                                    <span class="color-name"><?php echo $color['name']; ?></span>
                                </label>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Sizes -->
                    <?php if (!empty($sizes)): ?>
                    <div class="option-group">
                        <label class="option-label">المقاس:</label>
                        <div class="size-options">
                            <?php foreach ($sizes as $index => $size): ?>
                                <label class="size-option">
                                    <input type="radio" name="size_id" value="<?php echo $size['id']; ?>" 
                                           <?php echo $index === 0 ? 'checked' : ''; ?> required>
                                    <span class="size-label"><?php echo $size['name']; ?></span>
                                </label>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Quantity -->
                    <div class="option-group">
                        <label class="option-label">الكمية:</label>
                        <div class="quantity-selector">
                            <button type="button" class="qty-btn minus" onclick="changeQuantity(-1)">-</button>
                            <input type="number" name="quantity" value="1" min="1" max="10" id="quantityInput">
                            <button type="button" class="qty-btn plus" onclick="changeQuantity(1)">+</button>
                        </div>
                    </div>

                    <!-- Add to Cart Button -->
                    <div class="action-buttons">
                        <?php if ($product['stock_status'] === 'in_stock'): ?>
                            <button type="submit" class="btn btn-primary add-to-cart-btn">
                                <i class="fas fa-shopping-cart"></i>
                                أضف للسلة
                            </button>
                        <?php else: ?>
                            <button type="button" class="btn btn-disabled" disabled>
                                <i class="fas fa-times"></i>
                                غير متوفر
                            </button>
                        <?php endif; ?>
                        
                        <button type="button" class="btn btn-outline wishlist-btn">
                            <i class="far fa-heart"></i>
                            أضف للمفضلة
                        </button>
                    </div>
                </form>

                <!-- Product Info -->
                <div class="product-info">
                    <div class="info-item">
                        <i class="fas fa-truck"></i>
                        <span>شحن مجاني للطلبات أكثر من 500 ريال</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-undo"></i>
                        <span>إمكانية الإرجاع خلال 14 يوم</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>ضمان الجودة</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-headset"></i>
                        <span>دعم فني 24/7</span>
                    </div>
                </div>
            </div>
            </div>
        </div>

        <!-- Product Tabs -->
        <div class="container">
            <div class="product-tabs-section">
            <ul class="nav nav-tabs product-nav-tabs" id="productTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab">
                        <i class="fas fa-info-circle me-2"></i>التفاصيل
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab">
                        <i class="fas fa-star me-2"></i>الآراء والتقييمات
                    </button>
                </li>
            </ul>

            <div class="tab-content product-tab-content" id="productTabContent">
                <!-- Details Tab -->
                <div class="tab-pane fade show active" id="details" role="tabpanel">
                    <div class="details-content">
                        <h3>تفاصيل المنتج</h3>
                        <?php if (!empty($product['description'])): ?>
                            <div class="product-description">
                                <?php echo nl2br(htmlspecialchars($product['description'])); ?>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">لا توجد تفاصيل إضافية لهذا المنتج.</p>
                        <?php endif; ?>

                        <div class="product-specifications">
                            <h4>المواصفات</h4>
                            <div class="specs-grid">
                                <div class="spec-item">
                                    <span class="spec-label">التصنيف:</span>
                                    <span class="spec-value"><?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?></span>
                                </div>
                                <div class="spec-item">
                                    <span class="spec-label">حالة التوفر:</span>
                                    <span class="spec-value <?php echo $product['stock_status'] == 'in_stock' ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo $product['stock_status'] == 'in_stock' ? 'متوفر' : 'نفذ'; ?>
                                    </span>
                                </div>
                                <?php if (!empty($colors)): ?>
                                <div class="spec-item">
                                    <span class="spec-label">الألوان المتوفرة:</span>
                                    <span class="spec-value">
                                        <?php echo implode(', ', array_column($colors, 'name')); ?>
                                    </span>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($sizes)): ?>
                                <div class="spec-item">
                                    <span class="spec-label">المقاسات المتوفرة:</span>
                                    <span class="spec-value">
                                        <?php echo implode(', ', array_column($sizes, 'name')); ?>
                                    </span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reviews Tab -->
                <div class="tab-pane fade" id="reviews" role="tabpanel">
                    <div class="reviews-content">
                        <h3>آراء العملاء</h3>

                        <!-- Reviews Summary -->
                        <div class="reviews-summary">
                            <div class="rating-overview">
                                <div class="average-rating">
                                    <span class="rating-number">4.5</span>
                                    <div class="stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                    </div>
                                    <span class="reviews-count">من 24 تقييم</span>
                                </div>
                            </div>
                        </div>

                        <!-- Add Review Form -->
                        <div class="add-review-section">
                            <h4>أضف تقييمك</h4>
                            <form class="review-form">
                                <div class="rating-input">
                                    <label>التقييم:</label>
                                    <div class="star-rating">
                                        <input type="radio" name="rating" value="5" id="star5">
                                        <label for="star5"><i class="fas fa-star"></i></label>
                                        <input type="radio" name="rating" value="4" id="star4">
                                        <label for="star4"><i class="fas fa-star"></i></label>
                                        <input type="radio" name="rating" value="3" id="star3">
                                        <label for="star3"><i class="fas fa-star"></i></label>
                                        <input type="radio" name="rating" value="2" id="star2">
                                        <label for="star2"><i class="fas fa-star"></i></label>
                                        <input type="radio" name="rating" value="1" id="star1">
                                        <label for="star1"><i class="fas fa-star"></i></label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="reviewName">الاسم:</label>
                                    <input type="text" id="reviewName" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label for="reviewComment">التعليق:</label>
                                    <textarea id="reviewComment" class="form-control" rows="4" required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">إرسال التقييم</button>
                            </form>
                        </div>

                        <!-- Reviews List -->
                        <div class="reviews-list">
                            <div class="review-item">
                                <div class="review-header">
                                    <div class="reviewer-info">
                                        <span class="reviewer-name">سارة أحمد</span>
                                        <div class="review-rating">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                    </div>
                                    <span class="review-date">منذ أسبوع</span>
                                </div>
                                <p class="review-text">فستان رائع جداً، الجودة ممتازة والخامة فاخرة. أنصح بشرائه.</p>
                            </div>

                            <div class="review-item">
                                <div class="review-header">
                                    <div class="reviewer-info">
                                        <span class="reviewer-name">نور محمد</span>
                                        <div class="review-rating">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                        </div>
                                    </div>
                                    <span class="review-date">منذ أسبوعين</span>
                                </div>
                                <p class="review-text">جميل جداً ومناسب للمناسبات الخاصة. الشحن كان سريع.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تم نقل قسم صور الطبيعة ليكون مباشرة تحت معرض الصور -->
            </div>
        </div>
        </div>

        <!-- Related Products -->
        <?php if (!empty($related_products)): ?>
        <div class="related-products">
            <h2>منتجات مشابهة</h2>
            <div class="products-grid">
                <?php foreach ($related_products as $related): ?>
                <div class="product-card">
                    <a href="product_new.php?slug=<?php echo $related['slug']; ?>" class="product-link">
                        <?php if ($related['featured_image']): ?>
                            <img src="uploads/<?php echo $related['featured_image']; ?>" 
                                 alt="<?php echo $related['title']; ?>" 
                                 class="product-image">
                        <?php else: ?>
                            <div class="no-image">
                                <i class="fas fa-image"></i>
                            </div>
                        <?php endif; ?>
                        
                        <div class="product-info">
                            <h3 class="product-title"><?php echo $related['title']; ?></h3>
                            <div class="product-price">
                                <?php if ($related['sale_price'] && $related['sale_price'] > 0): ?>
                                    <span class="sale-price"><?php echo format_price($related['sale_price']); ?></span>
                                    <span class="original-price"><?php echo format_price($related['price']); ?></span>
                                <?php else: ?>
                                    <span class="current-price"><?php echo format_price($related['price']); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </a>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Product Page Styles */
.product-page {
    padding: 20px 0;
    background: #f8fafc;
    min-height: 80vh;
}

.breadcrumb {
    margin-bottom: 30px;
    padding: 15px 0;
    border-bottom: 1px solid #e5e7eb;
}

.breadcrumb a {
    color: #6366f1;
    text-decoration: none;
    margin: 0 5px;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb span {
    color: #9ca3af;
    margin: 0 5px;
}

.breadcrumb .current {
    color: #1f2937;
    font-weight: 500;
}

.product-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 60px;
    background: white;
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* Product Images */
.product-images {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.main-image {
    width: 100%;
    height: 500px;
    border-radius: 12px;
    overflow: hidden;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: #9ca3af;
    font-size: 18px;
}

.no-image i {
    font-size: 48px;
}

.thumbnail-images {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding: 5px 0;
}

.thumbnail {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    object-fit: cover;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.thumbnail:hover,
.thumbnail.active {
    border-color: #6366f1;
    transform: scale(1.05);
}

/* Product Details */
.product-details {
    display: flex;
    flex-direction: column;
    gap: 0px;
}

.product-title {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    line-height: 1.3;
    margin: 0;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.sale-price,
.current-price {
    font-size: 24px;
    font-weight: 700;
    color: #059669;
}

.original-price {
    font-size: 18px;
    color: #9ca3af;
    text-decoration: line-through;
}

.discount-badge {
    background: #ef4444;
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
}

.product-description {
    color: #6b7280;
    line-height: 1.6;
    font-size: 16px;
}

/* Product Options */
.product-options {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.option-label {
    font-weight: 600;
    color: #374151;
    font-size: 16px;
}

/* Color Options */
.color-options {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.color-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.color-option:hover {
    border-color: #6366f1;
}

.color-option input[type="radio"] {
    display: none;
}

.color-option input[type="radio"]:checked + .color-swatch {
    transform: scale(1.2);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

.color-option input[type="radio"]:checked ~ .color-name {
    color: #6366f1;
    font-weight: 600;
}

.color-swatch {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
}

.color-name {
    font-size: 14px;
    color: #374151;
}

/* Size Options */
.size-options {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.size-option {
    cursor: pointer;
}

.size-option input[type="radio"] {
    display: none;
}

.size-label {
    display: block;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 50px;
}

.size-option:hover .size-label {
    border-color: #6366f1;
}

.size-option input[type="radio"]:checked + .size-label {
    border-color: #6366f1;
    background: #6366f1;
    color: white;
}

/* Quantity Selector */
.quantity-selector {
    display: flex;
    align-items: center;
    gap: 0;
    width: fit-content;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.qty-btn {
    background: #f3f4f6;
    border: none;
    padding: 12px 16px;
    cursor: pointer;
    font-size: 18px;
    font-weight: 600;
    color: #374151;
    transition: all 0.3s ease;
}

.qty-btn:hover {
    background: #e5e7eb;
}

#quantityInput {
    border: none;
    padding: 12px 16px;
    text-align: center;
    width: 80px;
    font-size: 16px;
    font-weight: 600;
}

#quantityInput:focus {
    outline: none;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 14px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 160px;
}

.btn-primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

.btn-outline {
    background: white;
    color: #6366f1;
    border: 2px solid #6366f1;
}

.btn-outline:hover {
    background: #6366f1;
    color: white;
}

.btn-disabled {
    background: #9ca3af;
    color: white;
    cursor: not-allowed;
}

/* Product Info */
.product-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 12px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #374151;
    font-size: 14px;
}

.info-item i {
    color: #6366f1;
    width: 20px;
}

/* Related Products */
.related-products {
    margin-top: 60px;
}

.related-products h2 {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 30px;
    text-align: center;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.product-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-link {
    display: block;
    text-decoration: none;
    color: inherit;
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.product-card .product-info {
    padding: 20px;
    background: white;
}

.product-card .product-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 10px;
    line-height: 1.4;
}

.product-card .product-price {
    display: flex;
    align-items: center;
    gap: 8px;
}

.product-card .sale-price,
.product-card .current-price {
    font-size: 18px;
    font-weight: 700;
    color: #059669;
}

.product-card .original-price {
    font-size: 14px;
    color: #9ca3af;
    text-decoration: line-through;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 0;
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    position: relative;
    animation: slideIn 0.3s ease;
}

.close {
    position: absolute;
    right: 20px;
    top: 15px;
    color: #9ca3af;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1;
}

.close:hover {
    color: #374151;
}

.modal-body {
    padding: 40px 30px;
    text-align: center;
}

.success-icon {
    font-size: 48px;
    color: #10b981;
    margin-bottom: 20px;
}

.modal-body h3 {
    font-size: 24px;
    color: #1f2937;
    margin-bottom: 15px;
}

.modal-body p {
    color: #6b7280;
    margin-bottom: 30px;
    line-height: 1.6;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Product Tabs */
.product-tabs-section {
    margin: 40px 0;
    background: white;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    max-width: 100%;
}

.product-nav-tabs {
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
    margin: 0;
}

.product-nav-tabs .nav-item {
    margin: 0;
}

.product-nav-tabs .nav-link {
    border: none;
    border-radius: 0;
    padding: 20px 30px;
    color: #6b7280;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.product-nav-tabs .nav-link:hover {
    background: rgba(99, 102, 241, 0.1);
    color: #6366f1;
}

.product-nav-tabs .nav-link.active {
    background: white;
    color: #6366f1;
    border-bottom: 3px solid #6366f1;
}

.product-tab-content {
    padding: 30px;
}

.details-content h3,
.reviews-content h3,
.lifestyle-content h3 {
    color: #1f2937;
    margin-bottom: 20px;
    font-weight: 600;
}

.product-description {
    line-height: 1.8;
    color: #4b5563;
    margin-bottom: 30px;
}

.product-specifications h4 {
    color: #374151;
    margin-bottom: 15px;
    font-weight: 600;
}

.specs-grid {
    display: grid;
    gap: 15px;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
}

.spec-label {
    font-weight: 500;
    color: #6b7280;
}

.spec-value {
    color: #1f2937;
    font-weight: 500;
}

/* Reviews Styles */
.reviews-summary {
    background: #f9fafb;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.rating-overview {
    text-align: center;
}

.average-rating {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.rating-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #fbbf24;
}

.stars {
    color: #fbbf24;
    font-size: 1.2rem;
}

.reviews-count {
    color: #6b7280;
    font-size: 0.9rem;
}

.add-review-section {
    margin: 30px 0;
    padding: 20px;
    background: #f9fafb;
    border-radius: 10px;
}

.review-form .form-group {
    margin-bottom: 20px;
}

.review-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
}

.review-form .form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
}

.star-rating {
    display: flex;
    flex-direction: row-reverse;
    gap: 5px;
    margin-top: 5px;
}

.star-rating input {
    display: none;
}

.star-rating label {
    cursor: pointer;
    color: #d1d5db;
    font-size: 1.5rem;
    transition: color 0.2s;
}

.star-rating input:checked ~ label,
.star-rating label:hover,
.star-rating label:hover ~ label {
    color: #fbbf24;
}

.reviews-list {
    margin-top: 30px;
}

.review-item {
    padding: 20px 0;
    border-bottom: 1px solid #e5e7eb;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.reviewer-name {
    font-weight: 600;
    color: #1f2937;
}

.review-rating {
    color: #fbbf24;
}

.review-date {
    color: #6b7280;
    font-size: 0.9rem;
}

.review-text {
    color: #4b5563;
    line-height: 1.6;
    margin: 0;
}

/* Lifestyle Dropdown under title */
.lifestyle-dropdown-title {
    margin: 15px 0 20px 0;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: box-shadow 0.3s ease;
}

.lifestyle-dropdown-title:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.lifestyle-toggle {
    width: 100%;
    padding: 12px 18px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
    font-weight: 500;
    color: #475569;
}

.lifestyle-toggle:hover {
    background: #f3f4f6;
}

.lifestyle-toggle i.fas.fa-camera {
    color: #6366f1;
    margin-left: 8px;
}

.lifestyle-toggle span {
    flex: 1;
    text-align: right;
}

.lifestyle-toggle .toggle-icon {
    transition: transform 0.3s ease;
    color: #9ca3af;
}

.lifestyle-toggle.active .toggle-icon {
    transform: rotate(180deg);
}

.lifestyle-content {
    max-height: 0;
    overflow: hidden;
    transition: all 0.4s ease;
    background: white;
    opacity: 0;
}

.lifestyle-content.open {
    max-height: 500px;
    padding: 15px;
    opacity: 1;
    border-top: 1px solid #f3f4f6;
}

.lifestyle-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
    gap: 12px;
    max-width: 100%;
}

.lifestyle-thumb {
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.lifestyle-thumb:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.lifestyle-thumb img {
    width: 100%;
    height: 90px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.lifestyle-thumb:hover img {
    transform: scale(1.1);
}

.lifestyle-thumb::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0);
    transition: background 0.3s ease;
}

.lifestyle-thumb:hover::after {
    background: rgba(0,0,0,0.1);
}

/* Product Video Section */
.product-video-section {
    margin: 15px 0 20px 0;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: box-shadow 0.3s ease;
}

.product-video-section:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.video-toggle {
    width: 100%;
    padding: 12px 18px;
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
    font-weight: 500;
    color: #92400e;
}

.video-toggle:hover {
    background: linear-gradient(135deg, #fde68a 0%, #fcd34d 100%);
}

.video-toggle i.fas.fa-play-circle {
    color: #dc2626;
    margin-left: 8px;
    font-size: 16px;
}

.video-toggle span {
    flex: 1;
    text-align: right;
}

.video-toggle .toggle-icon {
    transition: transform 0.3s ease;
    color: #92400e;
}

.video-toggle.active .toggle-icon {
    transform: rotate(180deg);
}

.video-content {
    max-height: 0;
    overflow: hidden;
    transition: all 0.4s ease;
    background: white;
    opacity: 0;
}

.video-content.open {
    max-height: 400px;
    padding: 15px;
    opacity: 1;
    border-top: 1px solid #f3f4f6;
}

.video-wrapper {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.video-wrapper iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

.video-wrapper video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    background: #000;
}

/* Responsive Design */
@media (max-width: 768px) {
    .product-content {
        grid-template-columns: 1fr;
        gap: 30px;
        padding: 20px;
    }

    .main-image {
        height: 300px;
    }

    .product-title {
        font-size: 24px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }

    .color-options,
    .size-options {
        justify-content: center;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .product-nav-tabs .nav-link {
        padding: 15px 20px;
        font-size: 14px;
    }

    .product-tab-content {
        padding: 20px;
    }

    .lifestyle-toggle {
        padding: 10px 15px;
        font-size: 12px;
    }

    .lifestyle-grid {
        grid-template-columns: repeat(auto-fill, minmax(75px, 1fr));
        gap: 8px;
    }

    .lifestyle-thumb img {
        height: 75px;
    }

    .lifestyle-dropdown-title {
        margin: 10px 0 15px 0;
    }

    .video-toggle {
        padding: 10px 15px;
        font-size: 12px;
    }

    .product-video-section {
        margin: 10px 0 15px 0;
    }

    .video-content.open {
        max-height: 300px;
        padding: 10px;
    }
}

@media (max-width: 480px) {
    .product-page {
        padding: 10px 0;
    }

    .breadcrumb {
        font-size: 14px;
        padding: 10px 0;
    }

    .product-content {
        padding: 15px;
    }

    .main-image {
        height: 250px;
    }

    .product-title {
        font-size: 20px;
    }

    .sale-price,
    .current-price {
        font-size: 20px;
    }
}
</style>

<script>
// Product page functionality
document.addEventListener('DOMContentLoaded', function() {
    initProductPage();
});

function initProductPage() {
    // Add to cart form submission
    const addToCartForm = document.getElementById('addToCartForm');
    if (addToCartForm) {
        addToCartForm.addEventListener('submit', handleAddToCart);
    }

    // Close modal functionality
    const modal = document.getElementById('successModal');
    const closeBtn = modal.querySelector('.close');

    if (closeBtn) {
        closeBtn.addEventListener('click', closeModal);
    }

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            closeModal();
        }
    });
}

function changeMainImage(src) {
    const mainImage = document.getElementById('mainProductImage');
    if (mainImage) {
        mainImage.src = src;
    }

    // Update active thumbnail
    const thumbnails = document.querySelectorAll('.thumbnail');
    thumbnails.forEach(thumb => {
        thumb.classList.remove('active');
        if (thumb.src === src) {
            thumb.classList.add('active');
        }
    });
}

function changeQuantity(change) {
    const quantityInput = document.getElementById('quantityInput');
    if (quantityInput) {
        let currentValue = parseInt(quantityInput.value) || 1;
        let newValue = currentValue + change;

        if (newValue < 1) newValue = 1;
        if (newValue > 10) newValue = 10;

        quantityInput.value = newValue;
    }
}

function handleAddToCart(event) {
    event.preventDefault();

    const form = event.target;
    const submitBtn = form.querySelector('.add-to-cart-btn');
    const originalText = submitBtn.innerHTML;

    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإضافة...';
    submitBtn.disabled = true;

    // Prepare form data
    const formData = new FormData(form);

    // Send AJAX request
    fetch('ajax/add-to-cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success modal
            showSuccessModal();

            // Update cart count in header if exists
            updateCartCount(data.cart_count);

            // Show success toast
            showToast('تم إضافة المنتج للسلة بنجاح!', 'success');

        } else {
            // Show error message
            showToast(data.message || 'حدث خطأ في إضافة المنتج للسلة', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'error');
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function showSuccessModal() {
    const modal = document.getElementById('successModal');
    if (modal) {
        modal.style.display = 'block';
    }
}

function closeModal() {
    const modal = document.getElementById('successModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function updateCartCount(count) {
    const cartCountElements = document.querySelectorAll('.cart-count');
    cartCountElements.forEach(element => {
        element.textContent = count;
        if (count > 0) {
            element.style.display = 'inline';
        }
    });
}

function showToast(message, type = 'info') {
    // Remove existing toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add toast styles
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10001;
        display: flex;
        align-items: center;
        gap: 12px;
        max-width: 400px;
        animation: slideInRight 0.3s ease;
    `;

    // Add animation styles if not exists
    if (!document.querySelector('#toast-styles')) {
        const styles = document.createElement('style');
        styles.id = 'toast-styles';
        styles.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            .toast-content {
                display: flex;
                align-items: center;
                gap: 8px;
                flex: 1;
            }
            .toast-close {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                opacity: 0.8;
            }
            .toast-close:hover {
                opacity: 1;
                background: rgba(255, 255, 255, 0.1);
            }
        `;
        document.head.appendChild(styles);
    }

    // Add to page
    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

// Function to open image in modal
function openImageModal(imageSrc, altText = 'صورة المنتج') {
    // Create modal if it doesn't exist
    let modal = document.getElementById('imageModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'imageModal';
        modal.className = 'modal image-modal';
        modal.innerHTML = `
            <div class="modal-content image-modal-content">
                <div class="image-modal-header">
                    <span class="image-modal-title">${altText}</span>
                    <span class="close" onclick="closeImageModal()">&times;</span>
                </div>
                <div class="image-container">
                    <img id="modalImage" src="" alt="${altText}">
                </div>
            </div>
        `;
        document.body.appendChild(modal);

        // Add styles for image modal
        const styles = document.createElement('style');
        styles.textContent = `
            .image-modal {
                background: rgba(0, 0, 0, 0.9);
                backdrop-filter: blur(5px);
            }
            .image-modal-content {
                max-width: 95%;
                max-height: 95%;
                padding: 0;
                background: white;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                animation: modalZoomIn 0.3s ease;
            }
            .image-modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px 20px;
                background: #f8fafc;
                border-bottom: 1px solid #e5e7eb;
            }
            .image-modal-title {
                font-weight: 600;
                color: #374151;
                font-size: 16px;
            }
            .image-modal-content .close {
                color: #6b7280;
                font-size: 1.5rem;
                background: none;
                border: none;
                cursor: pointer;
                padding: 5px;
                border-radius: 50%;
                transition: all 0.3s ease;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .image-modal-content .close:hover {
                background: #f3f4f6;
                color: #374151;
            }
            .image-container {
                padding: 20px;
                text-align: center;
                background: white;
            }
            .image-modal-content img {
                max-width: 100%;
                max-height: 70vh;
                object-fit: contain;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            }
            @keyframes modalZoomIn {
                from { transform: scale(0.7); opacity: 0; }
                to { transform: scale(1); opacity: 1; }
            }
            @media (max-width: 768px) {
                .image-modal-content {
                    max-width: 98%;
                    max-height: 98%;
                }
                .image-modal-header {
                    padding: 10px 15px;
                }
                .image-modal-title {
                    font-size: 14px;
                }
                .image-container {
                    padding: 15px;
                }
                .image-modal-content img {
                    max-height: 60vh;
                }
            }
        `;
        document.head.appendChild(styles);
    }

    // Set image source and title
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('modalImage').alt = altText;
    document.querySelector('.image-modal-title').textContent = altText;

    // Show modal with animation
    modal.style.display = 'block';
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

function closeImageModal() {
    const modal = document.getElementById('imageModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Close image modal when clicking outside
window.addEventListener('click', function(event) {
    const modal = document.getElementById('imageModal');
    if (event.target === modal) {
        closeImageModal();
    }
});

// Toggle lifestyle images dropdown
function toggleLifestyleImages() {
    const content = document.getElementById('lifestyleContent');
    const toggle = document.querySelector('.lifestyle-toggle');

    if (content.classList.contains('open')) {
        content.classList.remove('open');
        toggle.classList.remove('active');
    } else {
        content.classList.add('open');
        toggle.classList.add('active');
    }
}

// Toggle product video
function toggleProductVideo() {
    const content = document.getElementById('videoContent');
    const toggle = document.querySelector('.video-toggle');

    if (content.classList.contains('open')) {
        content.classList.remove('open');
        toggle.classList.remove('active');
    } else {
        content.classList.add('open');
        toggle.classList.add('active');
    }
}
</script>

<?php include 'includes/footer.php'; ?>

<!-- Success Message Modal -->
<div id="successModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <div class="modal-body">
            <i class="fas fa-check-circle success-icon"></i>
            <h3>تم إضافة المنتج للسلة بنجاح!</h3>
            <p>يمكنك متابعة التسوق أو الذهاب للسلة لإتمام الطلب.</p>
            <div class="modal-actions">
                <a href="cart.php" class="btn btn-primary">عرض السلة</a>
                <button onclick="closeModal()" class="btn btn-outline">متابعة التسوق</button>
            </div>
        </div>
    </div>
</div>
