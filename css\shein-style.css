/* SHEIN-Inspired Modern E-commerce Design - 2025 */

/* Reset & Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 14px;
}

body {
    font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
    background: #ffffff;
    color: #000000;
    line-height: 1.4;
    direction: rtl;
    font-size: 14px;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    overflow-x: hidden;
}

/* SHEIN Color System */
:root {
    /* Brand Colors */
    --shein-black: #000000;
    --shein-white: #ffffff;
    --shein-pink: #ff6b9d;
    --shein-gray: #f5f5f5;
    --shein-light-gray: #fafafa;
    --shein-border: #e0e0e0;
    --shein-text: #333333;
    --shein-text-light: #666666;
    --shein-red: #ff4757;
    --shein-green: #2ed573;
    
    /* Spacing */
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-5: 20px;
    --space-6: 24px;
    --space-8: 32px;
    --space-10: 40px;
    
    /* Typography */
    --font-xs: 10px;
    --font-sm: 12px;
    --font-base: 14px;
    --font-lg: 16px;
    --font-xl: 18px;
    --font-2xl: 20px;
    --font-3xl: 24px;
    --font-4xl: 28px;
    
    /* Transitions */
    --transition: 0.2s ease;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

/* SHEIN Header */
.shein-header {
    background: var(--shein-white);
    border-bottom: 1px solid var(--shein-border);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-top {
    background: var(--shein-black);
    color: var(--shein-white);
    padding: var(--space-2) 0;
    font-size: var(--font-xs);
    text-align: center;
}

.header-main {
    padding: var(--space-4) 0;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-6);
}

.logo {
    font-size: var(--font-3xl);
    font-weight: 900;
    color: var(--shein-black);
    text-decoration: none;
    letter-spacing: -1px;
}

.search-bar {
    flex: 1;
    max-width: 500px;
    position: relative;
}

.search-input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--shein-border);
    border-radius: 0;
    font-size: var(--font-base);
    outline: none;
    transition: var(--transition);
}

.search-input:focus {
    border-color: var(--shein-black);
}

.search-btn {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    padding: 0 var(--space-4);
    background: var(--shein-black);
    color: var(--shein-white);
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-5);
}

.header-action {
    position: relative;
    color: var(--shein-black);
    text-decoration: none;
    font-size: var(--font-lg);
    transition: var(--transition);
}

.header-action:hover {
    color: var(--shein-pink);
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--shein-red);
    color: var(--shein-white);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: var(--font-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* SHEIN Navigation */
.shein-nav {
    background: var(--shein-white);
    border-bottom: 1px solid var(--shein-border);
    padding: var(--space-3) 0;
}

.nav-links {
    display: flex;
    justify-content: center;
    gap: var(--space-8);
    list-style: none;
}

.nav-link {
    color: var(--shein-text);
    text-decoration: none;
    font-size: var(--font-base);
    font-weight: 500;
    padding: var(--space-2) var(--space-3);
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-link:hover,
.nav-link.active {
    color: var(--shein-black);
    font-weight: 600;
}

/* SHEIN Hero Section */
.shein-hero {
    background: var(--shein-light-gray);
    padding: var(--space-10) 0;
    text-align: center;
}

.hero-content h1 {
    font-size: var(--font-4xl);
    font-weight: 900;
    color: var(--shein-black);
    margin-bottom: var(--space-4);
    letter-spacing: -1px;
}

.hero-content p {
    font-size: var(--font-lg);
    color: var(--shein-text-light);
    margin-bottom: var(--space-6);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.shein-btn {
    display: inline-block;
    padding: var(--space-4) var(--space-8);
    background: var(--shein-black);
    color: var(--shein-white);
    text-decoration: none;
    font-size: var(--font-base);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.shein-btn:hover {
    background: var(--shein-text);
    transform: translateY(-1px);
}

.shein-btn.outline {
    background: transparent;
    color: var(--shein-black);
    border: 2px solid var(--shein-black);
}

.shein-btn.outline:hover {
    background: var(--shein-black);
    color: var(--shein-white);
}

/* SHEIN Product Grid */
.shein-products {
    padding: var(--space-10) 0;
}

.section-title {
    text-align: center;
    font-size: var(--font-3xl);
    font-weight: 900;
    color: var(--shein-black);
    margin-bottom: var(--space-8);
    text-transform: uppercase;
    letter-spacing: -0.5px;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
}

/* SHEIN Product Card */
.shein-product-card {
    background: var(--shein-white);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.shein-product-card:hover {
    transform: translateY(-2px);
}

.product-image {
    position: relative;
    aspect-ratio: 3/4;
    overflow: hidden;
    background: var(--shein-gray);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.shein-product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badges {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.badge {
    padding: var(--space-1) var(--space-2);
    font-size: var(--font-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge.sale {
    background: var(--shein-red);
    color: var(--shein-white);
}

.badge.new {
    background: var(--shein-green);
    color: var(--shein-white);
}

.product-info {
    padding: var(--space-4);
}

.product-title {
    font-size: var(--font-base);
    font-weight: 500;
    color: var(--shein-text);
    margin-bottom: var(--space-2);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-3);
}

.current-price {
    font-size: var(--font-lg);
    font-weight: 700;
    color: var(--shein-black);
}

.original-price {
    font-size: var(--font-base);
    color: var(--shein-text-light);
    text-decoration: line-through;
}

.add-to-cart {
    width: 100%;
    padding: var(--space-3);
    background: var(--shein-black);
    color: var(--shein-white);
    border: none;
    font-size: var(--font-sm);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: var(--transition);
}

.add-to-cart:hover {
    background: var(--shein-text);
}

/* SHEIN Categories */
.shein-categories {
    padding: var(--space-10) 0;
    background: var(--shein-light-gray);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
}

.category-card {
    text-decoration: none;
    color: inherit;
    display: block;
    transition: var(--transition);
}

.category-card:hover {
    transform: translateY(-2px);
}

.category-image {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
    background: var(--shein-gray);
}

.category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.category-card:hover .category-image img {
    transform: scale(1.05);
}

.no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--shein-text-light);
    font-size: var(--font-4xl);
}

.category-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: var(--shein-white);
    padding: var(--space-6) var(--space-4) var(--space-4);
    text-align: center;
}

.category-overlay h3 {
    font-size: var(--font-lg);
    font-weight: 600;
    margin-bottom: var(--space-2);
}

.shop-now {
    font-size: var(--font-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.9;
}

/* Breadcrumb */
.breadcrumb {
    background: var(--shein-light-gray);
    padding: var(--space-3) 0;
    font-size: var(--font-sm);
}

.breadcrumb a {
    color: var(--shein-text-light);
    text-decoration: none;
}

.breadcrumb a:hover {
    color: var(--shein-black);
}

.breadcrumb span {
    margin: 0 var(--space-2);
    color: var(--shein-text-light);
}

/* Category Header */
.category-header {
    padding: var(--space-8) 0;
    text-align: center;
    background: var(--shein-white);
}

.category-header h1 {
    font-size: var(--font-4xl);
    font-weight: 900;
    color: var(--shein-black);
    margin-bottom: var(--space-3);
    text-transform: uppercase;
}

.category-header p {
    color: var(--shein-text-light);
    font-size: var(--font-lg);
    margin-bottom: var(--space-4);
}

.products-count {
    color: var(--shein-text);
    font-size: var(--font-base);
    font-weight: 600;
}

/* Filter Bar */
.filter-bar {
    background: var(--shein-white);
    border-top: 1px solid var(--shein-border);
    border-bottom: 1px solid var(--shein-border);
    padding: var(--space-4) 0;
}

.filter-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-left {
    display: flex;
    gap: var(--space-4);
}

.filter-btn,
.sort-btn {
    background: transparent;
    border: 1px solid var(--shein-border);
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-sm);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.filter-btn:hover,
.sort-btn:hover {
    border-color: var(--shein-black);
}

.view-toggle {
    display: flex;
    gap: var(--space-1);
}

.view-btn {
    background: transparent;
    border: 1px solid var(--shein-border);
    padding: var(--space-2);
    cursor: pointer;
    transition: var(--transition);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn.active,
.view-btn:hover {
    background: var(--shein-black);
    color: var(--shein-white);
    border-color: var(--shein-black);
}

/* Product Actions */
.product-actions {
    position: absolute;
    top: var(--space-3);
    left: var(--space-3);
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    opacity: 0;
    transition: var(--transition);
}

.shein-product-card:hover .product-actions {
    opacity: 1;
}

.action-btn {
    background: var(--shein-white);
    border: 1px solid var(--shein-border);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: var(--font-sm);
}

.action-btn:hover {
    background: var(--shein-black);
    color: var(--shein-white);
    border-color: var(--shein-black);
}

/* No Products */
.no-products {
    text-align: center;
    padding: var(--space-10) 0;
}

.no-products h3 {
    font-size: var(--font-2xl);
    color: var(--shein-text);
    margin-bottom: var(--space-3);
}

.no-products p {
    color: var(--shein-text-light);
    margin-bottom: var(--space-6);
}

/* Product Details Page */
.product-details {
    padding: var(--space-8) 0;
}

.product-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-10);
    align-items: start;
}

/* Product Gallery */
.product-gallery {
    position: sticky;
    top: var(--space-6);
}

.main-image {
    aspect-ratio: 3/4;
    overflow: hidden;
    background: var(--shein-gray);
    margin-bottom: var(--space-4);
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.main-image .no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--shein-text-light);
    font-size: var(--font-4xl);
}

.image-thumbnails {
    display: flex;
    gap: var(--space-2);
    overflow-x: auto;
}

.thumbnail {
    width: 80px;
    height: 100px;
    object-fit: cover;
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition);
    border: 2px solid transparent;
}

.thumbnail:hover,
.thumbnail.active {
    opacity: 1;
    border-color: var(--shein-black);
}

/* Product Info Section */
.product-info-section {
    padding: var(--space-4) 0;
}

.product-title {
    font-size: var(--font-3xl);
    font-weight: 700;
    color: var(--shein-black);
    margin-bottom: var(--space-4);
    line-height: 1.3;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-5);
}

.stars {
    color: var(--shein-orange);
    font-size: var(--font-base);
}

.rating-text {
    color: var(--shein-text-light);
    font-size: var(--font-sm);
}

.product-price-section {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-5);
}

.product-price-section .current-price {
    font-size: var(--font-3xl);
    font-weight: 900;
    color: var(--shein-black);
}

.product-price-section .original-price {
    font-size: var(--font-xl);
    color: var(--shein-text-light);
    text-decoration: line-through;
}

.discount-badge {
    background: var(--shein-red);
    color: var(--shein-white);
    padding: var(--space-1) var(--space-2);
    font-size: var(--font-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.stock-status {
    margin-bottom: var(--space-6);
}

.in-stock {
    color: var(--shein-green);
    font-weight: 600;
}

.out-of-stock {
    color: var(--shein-red);
    font-weight: 600;
}

.product-description {
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-6);
    border-bottom: 1px solid var(--shein-border);
}

.product-description h3 {
    font-size: var(--font-lg);
    font-weight: 600;
    margin-bottom: var(--space-3);
    color: var(--shein-black);
}

.product-description p {
    color: var(--shein-text);
    line-height: 1.6;
}

/* Product Actions */
.product-actions-section {
    margin-bottom: var(--space-8);
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-5);
}

.quantity-selector label {
    font-weight: 600;
    color: var(--shein-black);
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 1px solid var(--shein-border);
}

.qty-btn {
    background: var(--shein-white);
    border: none;
    width: 40px;
    height: 40px;
    cursor: pointer;
    font-size: var(--font-lg);
    font-weight: 600;
    transition: var(--transition);
}

.qty-btn:hover {
    background: var(--shein-gray);
}

.qty-input {
    border: none;
    width: 60px;
    height: 40px;
    text-align: center;
    font-size: var(--font-base);
    font-weight: 600;
    outline: none;
}

.action-buttons {
    display: flex;
    gap: var(--space-4);
}

.add-to-cart-btn {
    flex: 1;
    background: var(--shein-black);
    color: var(--shein-white);
    border: none;
    padding: var(--space-4);
    font-size: var(--font-base);
    font-weight: 600;
    text-transform: uppercase;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

.add-to-cart-btn:hover:not(:disabled) {
    background: var(--shein-text);
}

.add-to-cart-btn:disabled {
    background: var(--shein-text-light);
    cursor: not-allowed;
}

.wishlist-btn {
    background: transparent;
    color: var(--shein-black);
    border: 2px solid var(--shein-black);
    padding: var(--space-4);
    font-size: var(--font-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    min-width: 160px;
    justify-content: center;
}

.wishlist-btn:hover {
    background: var(--shein-black);
    color: var(--shein-white);
}

/* Product Features */
.product-features {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    padding: var(--space-5);
    background: var(--shein-light-gray);
}

.feature {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    color: var(--shein-text);
    font-size: var(--font-sm);
}

.feature i {
    color: var(--shein-green);
    font-size: var(--font-base);
}

/* Related Products */
.related-products {
    padding: var(--space-10) 0;
    background: var(--shein-light-gray);
}

/* Cart Page */
.cart-section {
    padding: var(--space-8) 0;
    min-height: 60vh;
}

.page-title {
    font-size: var(--font-4xl);
    font-weight: 900;
    color: var(--shein-black);
    margin-bottom: var(--space-8);
    text-align: center;
    text-transform: uppercase;
}

.cart-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-10);
    margin-bottom: var(--space-8);
}

/* Cart Items */
.cart-items {
    background: var(--shein-white);
    border: 1px solid var(--shein-border);
}

.cart-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 50px;
    gap: var(--space-4);
    padding: var(--space-4);
    background: var(--shein-light-gray);
    font-weight: 600;
    font-size: var(--font-sm);
    text-transform: uppercase;
    border-bottom: 1px solid var(--shein-border);
}

.cart-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 50px;
    gap: var(--space-4);
    padding: var(--space-5);
    border-bottom: 1px solid var(--shein-border);
    align-items: center;
}

.item-product {
    display: flex;
    gap: var(--space-4);
    align-items: center;
}

.item-image {
    width: 80px;
    height: 100px;
    background: var(--shein-gray);
    overflow: hidden;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-image .no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--shein-text-light);
    font-size: var(--font-xl);
}

.item-details h3 {
    font-size: var(--font-base);
    font-weight: 600;
    color: var(--shein-black);
    margin-bottom: var(--space-1);
    line-height: 1.3;
}

.item-sku {
    font-size: var(--font-xs);
    color: var(--shein-text-light);
}

.item-price,
.item-total {
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--shein-black);
}

.quantity-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.update-btn {
    background: var(--shein-black);
    color: var(--shein-white);
    border: none;
    padding: var(--space-1) var(--space-2);
    font-size: var(--font-xs);
    cursor: pointer;
    transition: var(--transition);
}

.update-btn:hover {
    background: var(--shein-text);
}

.remove-btn {
    background: transparent;
    border: none;
    color: var(--shein-red);
    font-size: var(--font-lg);
    cursor: pointer;
    padding: var(--space-2);
    transition: var(--transition);
}

.remove-btn:hover {
    color: var(--shein-text);
}

/* Cart Summary */
.cart-summary {
    background: var(--shein-white);
    border: 1px solid var(--shein-border);
    padding: var(--space-6);
    height: fit-content;
    position: sticky;
    top: var(--space-6);
}

.cart-summary h3 {
    font-size: var(--font-xl);
    font-weight: 700;
    color: var(--shein-black);
    margin-bottom: var(--space-5);
    text-transform: uppercase;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--space-3);
    font-size: var(--font-base);
}

.summary-total {
    display: flex;
    justify-content: space-between;
    padding: var(--space-4) 0;
    border-top: 2px solid var(--shein-black);
    margin: var(--space-4) 0;
    font-size: var(--font-lg);
    font-weight: 700;
    color: var(--shein-black);
}

.promo-code {
    display: flex;
    gap: var(--space-2);
    margin-bottom: var(--space-5);
}

.promo-input {
    flex: 1;
    padding: var(--space-3);
    border: 1px solid var(--shein-border);
    font-size: var(--font-sm);
    outline: none;
}

.promo-input:focus {
    border-color: var(--shein-black);
}

.promo-btn {
    background: var(--shein-black);
    color: var(--shein-white);
    border: none;
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-sm);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-transform: uppercase;
}

.promo-btn:hover {
    background: var(--shein-text);
}

.checkout-btn {
    width: 100%;
    background: var(--shein-black);
    color: var(--shein-white);
    border: none;
    padding: var(--space-4);
    font-size: var(--font-lg);
    font-weight: 700;
    text-transform: uppercase;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: var(--space-5);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

.checkout-btn:hover {
    background: var(--shein-text);
}

.payment-methods {
    text-align: center;
}

.payment-methods p {
    font-size: var(--font-xs);
    color: var(--shein-text-light);
    margin-bottom: var(--space-2);
}

.payment-icons {
    display: flex;
    justify-content: center;
    gap: var(--space-3);
    font-size: var(--font-xl);
    color: var(--shein-text-light);
}

/* Cart Actions */
.cart-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-5) 0;
    border-top: 1px solid var(--shein-border);
}

.continue-shopping {
    color: var(--shein-black);
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    transition: var(--transition);
}

.continue-shopping:hover {
    color: var(--shein-text-light);
}

.clear-cart {
    background: transparent;
    color: var(--shein-red);
    border: 1px solid var(--shein-red);
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-sm);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.clear-cart:hover {
    background: var(--shein-red);
    color: var(--shein-white);
}

/* Empty Cart */
.empty-cart {
    text-align: center;
    padding: var(--space-10) 0;
}

.empty-cart h3 {
    font-size: var(--font-2xl);
    color: var(--shein-text);
    margin-bottom: var(--space-3);
}

.empty-cart p {
    color: var(--shein-text-light);
    margin-bottom: var(--space-6);
}

/* Responsive */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--space-4);
    }

    .nav-links {
        gap: var(--space-4);
        flex-wrap: wrap;
    }

    .products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-3);
    }

    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-3);
    }

    .hero-content h1 {
        font-size: var(--font-3xl);
    }

    /* Product Details Mobile */
    .product-layout {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .product-gallery {
        position: static;
    }

    .action-buttons {
        flex-direction: column;
    }

    .wishlist-btn {
        min-width: auto;
    }

    /* Cart Mobile */
    .cart-layout {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .cart-header {
        display: none;
    }

    .cart-item {
        grid-template-columns: 1fr;
        gap: var(--space-4);
        padding: var(--space-4);
        border: 1px solid var(--shein-border);
        margin-bottom: var(--space-3);
    }

    .item-product {
        flex-direction: column;
        text-align: center;
    }

    .item-image {
        width: 120px;
        height: 150px;
        margin: 0 auto;
    }

    .quantity-form {
        align-items: center;
    }

    .cart-actions {
        flex-direction: column;
        gap: var(--space-4);
        text-align: center;
    }

    /* Filter Bar Mobile */
    .filter-content {
        flex-direction: column;
        gap: var(--space-4);
    }

    .filter-left {
        justify-content: center;
    }
}
