/* SHEIN-Inspired Modern E-commerce Design - 2025 */

/* Reset & Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 14px;
}

body {
    font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
    background: #ffffff;
    color: #000000;
    line-height: 1.4;
    direction: rtl;
    font-size: 14px;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    overflow-x: hidden;
}

/* SHEIN Color System */
:root {
    /* Brand Colors */
    --shein-black: #000000;
    --shein-white: #ffffff;
    --shein-pink: #ff6b9d;
    --shein-gray: #f5f5f5;
    --shein-light-gray: #fafafa;
    --shein-border: #e0e0e0;
    --shein-text: #333333;
    --shein-text-light: #666666;
    --shein-red: #ff4757;
    --shein-green: #2ed573;
    
    /* Spacing */
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-5: 20px;
    --space-6: 24px;
    --space-8: 32px;
    --space-10: 40px;
    
    /* Typography */
    --font-xs: 10px;
    --font-sm: 12px;
    --font-base: 14px;
    --font-lg: 16px;
    --font-xl: 18px;
    --font-2xl: 20px;
    --font-3xl: 24px;
    --font-4xl: 28px;
    
    /* Transitions */
    --transition: 0.2s ease;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

/* SHEIN Header */
.shein-header {
    background: var(--shein-white);
    border-bottom: 1px solid var(--shein-border);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-top {
    background: var(--shein-black);
    color: var(--shein-white);
    padding: var(--space-2) 0;
    font-size: var(--font-xs);
    text-align: center;
}

.header-main {
    padding: var(--space-4) 0;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-6);
}

.logo {
    font-size: var(--font-3xl);
    font-weight: 900;
    color: var(--shein-black);
    text-decoration: none;
    letter-spacing: -1px;
}

.search-bar {
    flex: 1;
    max-width: 500px;
    position: relative;
}

.search-input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--shein-border);
    border-radius: 0;
    font-size: var(--font-base);
    outline: none;
    transition: var(--transition);
}

.search-input:focus {
    border-color: var(--shein-black);
}

.search-btn {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    padding: 0 var(--space-4);
    background: var(--shein-black);
    color: var(--shein-white);
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-5);
}

.header-action {
    position: relative;
    color: var(--shein-black);
    text-decoration: none;
    font-size: var(--font-lg);
    transition: var(--transition);
}

.header-action:hover {
    color: var(--shein-pink);
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--shein-red);
    color: var(--shein-white);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: var(--font-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* SHEIN Navigation */
.shein-nav {
    background: var(--shein-white);
    border-bottom: 1px solid var(--shein-border);
    padding: var(--space-3) 0;
}

.nav-links {
    display: flex;
    justify-content: center;
    gap: var(--space-8);
    list-style: none;
}

.nav-link {
    color: var(--shein-text);
    text-decoration: none;
    font-size: var(--font-base);
    font-weight: 500;
    padding: var(--space-2) var(--space-3);
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-link:hover,
.nav-link.active {
    color: var(--shein-black);
    font-weight: 600;
}

/* SHEIN Hero Section */
.shein-hero {
    background: var(--shein-light-gray);
    padding: var(--space-10) 0;
    text-align: center;
}

.hero-content h1 {
    font-size: var(--font-4xl);
    font-weight: 900;
    color: var(--shein-black);
    margin-bottom: var(--space-4);
    letter-spacing: -1px;
}

.hero-content p {
    font-size: var(--font-lg);
    color: var(--shein-text-light);
    margin-bottom: var(--space-6);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.shein-btn {
    display: inline-block;
    padding: var(--space-4) var(--space-8);
    background: var(--shein-black);
    color: var(--shein-white);
    text-decoration: none;
    font-size: var(--font-base);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.shein-btn:hover {
    background: var(--shein-text);
    transform: translateY(-1px);
}

.shein-btn.outline {
    background: transparent;
    color: var(--shein-black);
    border: 2px solid var(--shein-black);
}

.shein-btn.outline:hover {
    background: var(--shein-black);
    color: var(--shein-white);
}

/* SHEIN Product Grid */
.shein-products {
    padding: var(--space-10) 0;
}

.section-title {
    text-align: center;
    font-size: var(--font-3xl);
    font-weight: 900;
    color: var(--shein-black);
    margin-bottom: var(--space-8);
    text-transform: uppercase;
    letter-spacing: -0.5px;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
}

/* SHEIN Product Card */
.shein-product-card {
    background: var(--shein-white);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.shein-product-card:hover {
    transform: translateY(-2px);
}

.product-image {
    position: relative;
    aspect-ratio: 3/4;
    overflow: hidden;
    background: var(--shein-gray);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.shein-product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badges {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.badge {
    padding: var(--space-1) var(--space-2);
    font-size: var(--font-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge.sale {
    background: var(--shein-red);
    color: var(--shein-white);
}

.badge.new {
    background: var(--shein-green);
    color: var(--shein-white);
}

.product-info {
    padding: var(--space-4);
}

.product-title {
    font-size: var(--font-base);
    font-weight: 500;
    color: var(--shein-text);
    margin-bottom: var(--space-2);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-3);
}

.current-price {
    font-size: var(--font-lg);
    font-weight: 700;
    color: var(--shein-black);
}

.original-price {
    font-size: var(--font-base);
    color: var(--shein-text-light);
    text-decoration: line-through;
}

.add-to-cart {
    width: 100%;
    padding: var(--space-3);
    background: var(--shein-black);
    color: var(--shein-white);
    border: none;
    font-size: var(--font-sm);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: var(--transition);
}

.add-to-cart:hover {
    background: var(--shein-text);
}

/* Responsive */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--space-4);
    }
    
    .nav-links {
        gap: var(--space-4);
        flex-wrap: wrap;
    }
    
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-3);
    }
    
    .hero-content h1 {
        font-size: var(--font-3xl);
    }
}
