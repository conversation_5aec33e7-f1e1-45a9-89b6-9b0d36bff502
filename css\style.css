/* Modern Elegant E-commerce Design - 2025 */

/* Reset & Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #fafafa;
    color: #1a1a1a;
    line-height: 1.7;
    direction: rtl;
    font-size: 15px;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Modern Color System */
:root {
    /* Primary Colors - Elegant Purple/Pink Gradient */
    --primary: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);

    /* Secondary Colors - Warm Accent */
    --secondary: #f59e0b;
    --secondary-light: #fbbf24;
    --secondary-dark: #d97706;

    /* Success & Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* Neutral Colors - Modern Gray Scale */
    --dark: #111827;
    --gray-900: #1f2937;
    --gray-800: #374151;
    --gray-700: #4b5563;
    --gray-600: #6b7280;
    --gray-500: #9ca3af;
    --gray-400: #d1d5db;
    --gray-300: #e5e7eb;
    --gray-200: #f3f4f6;
    --gray-100: #f9fafb;
    --white: #ffffff;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --bg-dark: #111827;

    /* Border Colors */
    --border-light: #e5e7eb;
    --border-medium: #d1d5db;
    --border-dark: #9ca3af;

    /* Shadow System */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Spacing System */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;

    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;

    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }

.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-end { justify-content: flex-end; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.transition { transition: var(--transition-normal); }
.transition-fast { transition: var(--transition-fast); }
.transition-slow { transition: var(--transition-slow); }

/* Modern Top Bar */
.top-bar {
    background: var(--bg-dark);
    color: var(--white);
    padding: var(--space-sm) 0;
    font-size: var(--font-size-xs);
    border-bottom: 1px solid var(--gray-800);
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--space-lg);
}

.top-bar-left,
.top-bar-right {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.contact-info {
    display: flex;
    gap: var(--space-lg);
}

.contact-info span {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--gray-300);
    transition: var(--transition-fast);
}

.contact-info span:hover {
    color: var(--white);
}

.contact-info i {
    font-size: var(--font-size-xs);
    opacity: 0.8;
}

.social-links {
    display: flex;
    gap: var(--space-sm);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    color: var(--gray-300);
    font-size: var(--font-size-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    text-decoration: none;
}

.social-link:hover {
    color: var(--white);
    background: var(--primary);
    transform: translateY(-1px);
}

.language-selector select {
    background: var(--gray-800);
    border: 1px solid var(--gray-700);
    color: var(--white);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: var(--transition-fast);
}

.language-selector select:hover {
    border-color: var(--primary);
}

.language-selector select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

@media (max-width: 768px) {
    .top-bar {
        display: none;
    }
}

/* Modern Header Design */
.main-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    padding: var(--space-lg) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: var(--transition-fast);
}

.main-header.scrolled {
    padding: var(--space-md) 0;
    box-shadow: var(--shadow-md);
    background: rgba(255, 255, 255, 0.95);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-xl);
}

.header-logo {
    flex-shrink: 0;
    z-index: 1001;
}

.header-logo a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--dark);
    transition: var(--transition-fast);
}

.header-logo a:hover {
    transform: scale(1.02);
}

.logo-img {
    height: 45px;
    width: auto;
    object-fit: contain;
}

.logo-text {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.5px;
    line-height: 1;
}

/* Modern Search Bar */
.header-search {
    flex: 1;
    max-width: 600px;
    margin: 0 var(--space-xl);
}

.search-form {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 100%;
    padding: var(--space-md) var(--space-3xl) var(--space-md) var(--space-lg);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-full);
    font-size: var(--font-size-base);
    background: var(--bg-secondary);
    transition: var(--transition-normal);
    font-family: inherit;
    color: var(--dark);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary);
    background: var(--white);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

.search-input::placeholder {
    color: var(--gray-500);
    font-weight: 400;
}

.search-btn {
    position: absolute;
    right: var(--space-xs);
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-gradient);
    border: none;
    color: var(--white);
    cursor: pointer;
    font-size: var(--font-size-lg);
    padding: var(--space-md);
    border-radius: var(--radius-full);
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.search-btn:hover {
    transform: translateY(-50%) scale(1.05);
    box-shadow: var(--shadow-md);
}

.search-btn:active {
    transform: translateY(-50%) scale(0.98);
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    margin-top: var(--space-sm);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-normal);
    z-index: 1002;
    max-height: 300px;
    overflow-y: auto;
}

.search-suggestions.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.suggestion-item {
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid var(--border-light);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover {
    background: var(--bg-secondary);
}

.suggestion-item i {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

@media (max-width: 768px) {
    .header-search {
        margin: 0 var(--space-md);
        max-width: none;
    }

    .search-input {
        padding: var(--space-sm) var(--space-2xl) var(--space-sm) var(--space-md);
        font-size: var(--font-size-sm);
    }

    .search-btn {
        width: 36px;
        height: 36px;
        font-size: var(--font-size-base);
    }
}

/* Modern Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    flex-shrink: 0;
}

.header-action {
    position: relative;
}

.action-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-xs);
    text-decoration: none;
    color: var(--gray-700);
    transition: var(--transition-fast);
    padding: var(--space-sm);
    border-radius: var(--radius-lg);
    min-width: 60px;
    position: relative;
}

.action-link:hover {
    color: var(--primary);
    background: var(--bg-secondary);
    transform: translateY(-2px);
}

.action-link i {
    font-size: var(--font-size-xl);
    transition: var(--transition-fast);
}

.action-link:hover i {
    transform: scale(1.1);
}

.action-text {
    font-size: var(--font-size-xs);
    font-weight: 500;
    line-height: 1;
    opacity: 0.8;
}

.action-count {
    position: absolute;
    top: var(--space-xs);
    left: var(--space-lg);
    background: var(--error);
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    animation: pulse 2s infinite;
    box-shadow: 0 0 0 2px var(--white);
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--dark);
    font-size: var(--font-size-xl);
    padding: var(--space-sm);
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.mobile-menu-toggle:hover {
    background: var(--bg-secondary);
    color: var(--primary);
}

@media (max-width: 768px) {
    .header-actions {
        gap: var(--space-sm);
    }

    .action-link {
        min-width: 50px;
        padding: var(--space-xs);
    }

    .action-link i {
        font-size: var(--font-size-lg);
    }

    .action-text {
        font-size: 10px;
    }

    .action-count {
        top: 2px;
        left: var(--space-md);
        min-width: 16px;
        height: 16px;
        font-size: 10px;
    }

    .mobile-menu-toggle {
        display: block;
    }
}

/* Modern Dropdown System */
.dropdown-toggle {
    background: none;
    border: none;
    cursor: pointer;
    transition: var(--transition-fast);
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + var(--space-sm));
    right: 0;
    background: var(--white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    min-width: 220px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-15px) scale(0.95);
    transition: var(--transition-normal);
    z-index: 1001;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.dropdown-menu::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px;
    background: var(--white);
    border: 1px solid var(--border-light);
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md) var(--space-lg);
    text-decoration: none;
    color: var(--gray-700);
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
    font-weight: 500;
    border-bottom: 1px solid var(--border-light);
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: var(--bg-secondary);
    color: var(--primary);
    transform: translateX(-2px);
}

.dropdown-item i {
    font-size: var(--font-size-base);
    width: 20px;
    text-align: center;
    opacity: 0.7;
    transition: var(--transition-fast);
}

.dropdown-item:hover i {
    opacity: 1;
    color: var(--primary);
}

.dropdown-divider {
    height: 1px;
    background: var(--border-light);
    margin: var(--space-sm) 0;
}

/* Modern Navigation System */
.main-nav {
    background: var(--white);
    border-bottom: 1px solid var(--border-light);
    padding: 0;
    position: relative;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2xl);
    padding: var(--space-lg) 0;
    position: relative;
}

.nav-link {
    text-decoration: none;
    color: var(--gray-700);
    font-weight: 600;
    font-size: var(--font-size-base);
    padding: var(--space-md) var(--space-lg);
    position: relative;
    transition: var(--transition-fast);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary);
    background: rgba(99, 102, 241, 0.05);
    transform: translateY(-1px);
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: -var(--space-lg);
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
    transition: var(--transition-normal);
}

.nav-link:hover::before,
.nav-link.active::before {
    width: 80%;
}

.nav-link i {
    font-size: var(--font-size-sm);
    opacity: 0.7;
    transition: var(--transition-fast);
}

.nav-link:hover i,
.nav-link.active i {
    opacity: 1;
}

/* Mobile Navigation */
.mobile-nav {
    display: none;
    position: fixed;
    top: 0;
    right: -100%;
    width: 300px;
    height: 100vh;
    background: var(--white);
    box-shadow: var(--shadow-2xl);
    z-index: 9999;
    transition: var(--transition-normal);
    overflow-y: auto;
}

.mobile-nav.show {
    right: 0;
}

.mobile-nav-header {
    padding: var(--space-xl);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.mobile-nav-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.mobile-nav-close:hover {
    background: var(--bg-secondary);
    color: var(--primary);
}

.mobile-nav-links {
    padding: var(--space-lg);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md);
    text-decoration: none;
    color: var(--gray-700);
    font-weight: 500;
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-sm);
    transition: var(--transition-fast);
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
    background: var(--bg-secondary);
    color: var(--primary);
}

.mobile-nav-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9998;
    opacity: 0;
    transition: var(--transition-normal);
}

.mobile-nav-overlay.show {
    opacity: 1;
}

@media (max-width: 768px) {
    .main-nav {
        display: none;
    }

    .mobile-nav,
    .mobile-nav-overlay {
        display: block;
    }
}

/* Categories Menu - Modern Style */
.categories-menu {
    position: relative;
}

.categories-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
    font-size: 14px;
}

.categories-toggle:hover {
    background: var(--primary-light);
}

.categories-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border);
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 250px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s;
    z-index: 1001;
    max-height: 400px;
    overflow-y: auto;
}

.categories-menu:hover .categories-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.category-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    text-decoration: none;
    color: var(--dark);
    transition: all 0.3s;
    border-bottom: 1px solid var(--border);
    font-size: 14px;
}

.category-link:hover {
    background: var(--light-gray);
    color: var(--primary);
}

/* Main Menu - SHEIN Style */
.main-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.menu-link {
    text-decoration: none;
    color: var(--dark);
    font-weight: 500;
    padding: 8px 0;
    transition: all 0.3s;
    position: relative;
    font-size: 14px;
}

.menu-link:hover,
.menu-link.active {
    color: var(--primary);
}

.menu-link.active::after {
    content: '';
    position: absolute;
    bottom: -12px;
    right: 0;
    left: 0;
    height: 2px;
    background: var(--primary);
}

/* Special Offer Banner */
.special-offer {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, var(--secondary), var(--primary));
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Mobile Navigation */
.mobile-nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
}

.mobile-nav-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-navigation {
    position: fixed;
    top: 0;
    right: -100%;
    width: 300px;
    height: 100vh;
    background: white;
    z-index: 1999;
    transition: right 0.3s;
    overflow-y: auto;
}

.mobile-navigation.active {
    right: 0;
}

.mobile-nav-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
}

.mobile-nav-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #64748b;
}

.mobile-nav-content {
    padding: 20px;
}

.mobile-menu {
    list-style: none;
    margin-bottom: 24px;
}

.mobile-menu li {
    margin-bottom: 8px;
}

.mobile-menu a {
    display: block;
    padding: 12px 0;
    text-decoration: none;
    color: #2d3748;
    font-weight: 500;
    border-bottom: 1px solid #f1f5f9;
    transition: color 0.2s;
}

.mobile-menu a:hover {
    color: #6366f1;
}

.mobile-nav-categories h4 {
    margin-bottom: 12px;
    color: #64748b;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mobile-categories {
    list-style: none;
}

.mobile-categories li {
    margin-bottom: 4px;
}

.mobile-categories a {
    display: block;
    padding: 8px 0;
    text-decoration: none;
    color: #64748b;
    font-size: 14px;
    transition: color 0.2s;
}

.mobile-categories a:hover {
    color: #6366f1;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);
}

.btn-outline {
    background: transparent;
    color: #6366f1;
    border: 2px solid #6366f1;
}

.btn-outline:hover {
    background: #6366f1;
    color: white;
}

.btn-disabled {
    background: #e2e8f0;
    color: #9ca3af;
    cursor: not-allowed;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 500px;
    overflow: hidden;
    margin-bottom: 60px;
}

.hero-slider {
    position: relative;
    height: 100%;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s;
}

.hero-slide.active {
    opacity: 1;
}

.hero-image {
    width: 100%;
    height: 100%;
    position: relative;
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    background: rgba(0, 0, 0, 0.4);
}

.hero-text {
    color: white;
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.hero-text h1 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 16px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-text p {
    font-size: 18px;
    margin-bottom: 32px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
}

.hero-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    pointer-events: none;
}

.hero-prev,
.hero-next {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s;
    pointer-events: all;
    backdrop-filter: blur(10px);
}

.hero-prev:hover,
.hero-next:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.hero-indicators {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.2s;
}

.indicator.active {
    background: white;
}

/* Modern Sections Design */
.section-header {
    text-align: center;
    margin-bottom: var(--space-3xl);
    position: relative;
}

.section-header h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--dark);
    margin-bottom: var(--space-md);
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -var(--space-sm);
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
}

.section-footer {
    text-align: center;
    margin-top: var(--space-3xl);
}

/* Modern Categories Section */
.categories-section {
    padding: var(--space-3xl) 0;
    background: var(--bg-secondary);
    position: relative;
    overflow: hidden;
}

.categories-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23e5e7eb" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.5;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-xl);
    position: relative;
    z-index: 1;
}

.category-card {
    text-decoration: none;
    color: inherit;
    display: block;
    transition: var(--transition-normal);
    group: hover;
}

.category-card:hover {
    text-decoration: none;
    color: inherit;
    transform: translateY(-5px);
}

.category-box {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    height: 107px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.category-card:hover .category-box {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.category-image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

.category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.category-card:hover .category-image img {
    transform: scale(1.05);
}

.no-image {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #64748b;
    font-size: 1.5rem;
    text-align: center;
    padding: 20px;
}

.no-image i {
    font-size: 2rem;
    margin-bottom: 8px;
    opacity: 0.6;
}

.no-image span {
    font-size: 0.8rem;
    font-weight: 500;
    opacity: 0.8;
}

.category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(99, 102, 241, 0.8);
    transition: all 0.3s ease;
}

.category-card:hover .category-overlay {
    background: rgba(99, 102, 241, 0.9);
}

.category-title {
    text-align: center;
    margin-top: 10px;
}

.category-title h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    line-height: 1.4;
}
    opacity: 0;
    transition: opacity 0.3s;
}

.category-card:hover .category-overlay {
    opacity: 1;
}

.category-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    text-decoration: none;
    font-weight: 600;
    padding: 12px 24px;
    border: 2px solid white;
    border-radius: 25px;
    transition: all 0.2s;
}

.category-link:hover {
    background: white;
    color: #6366f1;
}

.category-info {
    padding: 24px;
}

.category-info h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 8px;
}

.category-info p {
    color: #64748b;
    font-size: 14px;
}

/* Products Section */
.featured-products-section,
.latest-products-section {
    padding: 60px 0;
}

.latest-products-section {
    background: #f8fafc;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
}

/* Modern Product Card Design */
.product-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    position: relative;
    border: 1px solid var(--border-light);
    group: hover;
}

.product-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary);
}

.product-image {
    position: relative;
    height: 320px;
    overflow: hidden;
    background: var(--bg-secondary);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.product-card:hover .product-image img {
    transform: scale(1.08);
}

.product-image .no-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--gray-400);
    background: var(--bg-tertiary);
}

.product-image .no-image i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--space-md);
}

.product-badges {
    position: absolute;
    top: var(--space-md);
    right: var(--space-md);
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
    z-index: 2;
}

.badge {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
}

.badge.featured {
    background: var(--primary-gradient);
    color: var(--white);
}

.badge.new {
    background: var(--success);
    color: var(--white);
}

.badge.sale {
    background: var(--error);
    color: var(--white);
}

.badge.out-of-stock {
    background: var(--gray-600);
    color: var(--white);
}

.badge.discount {
    background: var(--secondary);
    color: var(--white);
}

.product-actions {
    position: absolute;
    top: var(--space-md);
    left: var(--space-md);
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
    opacity: 0;
    transform: translateX(-10px);
    transition: var(--transition-normal);
    z-index: 2;
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateX(0);
}

.action-btn {
    width: 44px;
    height: 44px;
    border-radius: var(--radius-full);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid var(--border-light);
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    box-shadow: var(--shadow-sm);
}

.action-btn:hover {
    background: var(--primary);
    color: var(--white);
    transform: scale(1.1);
    border-color: var(--primary);
    box-shadow: var(--shadow-md);
}

.action-btn.wishlist.active {
    background: var(--error);
    color: var(--white);
    border-color: var(--error);
}

.product-info {
    padding: var(--space-xl);
    background: var(--white);
}

.product-title {
    margin-bottom: var(--space-sm);
}

.product-title a {
    text-decoration: none;
    color: var(--dark);
    font-size: var(--font-size-lg);
    font-weight: 600;
    transition: var(--transition-fast);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-title a:hover {
    color: var(--primary);
}

.product-price {
    margin-bottom: var(--space-md);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.current-price,
.sale-price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary);
}

.original-price {
    font-size: var(--font-size-base);
    color: var(--gray-400);
    text-decoration: line-through;
    font-weight: 500;
}

.discount-percentage {
    background: var(--error);
    color: var(--white);
    padding: 2px var(--space-xs);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-lg);
}

.stars {
    color: var(--secondary);
    display: flex;
    gap: 2px;
}

.stars i {
    font-size: var(--font-size-sm);
}

.rating-count {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    font-weight: 500;
}

.product-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-lg);
}

.stock-status {
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-full);
}

.stock-status.in-stock {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.stock-status.out-of-stock {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

.add-to-cart {
    width: 100%;
    padding: var(--space-md);
    background: var(--primary-gradient);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
}

.add-to-cart:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.add-to-cart:active {
    transform: translateY(0);
}

.add-to-cart:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Features Section */
.features-section {
    padding: 60px 0;
    background: #1a202c;
    color: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 32px;
}

.feature-card {
    text-align: center;
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 32px;
    color: white;
}

.feature-content h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
}

.feature-content p {
    color: #cbd5e0;
    font-size: 14px;
}

/* Footer Styles */
.main-footer {
    background: #1a202c;
    color: white;
}

.newsletter-section {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    padding: 60px 0;
}

.newsletter-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;
}

.newsletter-text h3 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
}

.newsletter-text p {
    opacity: 0.9;
    font-size: 16px;
}

.newsletter-form {
    flex: 1;
    max-width: 400px;
}

.newsletter-form .form-group {
    display: flex;
    gap: 12px;
}

.newsletter-input {
    flex: 1;
    padding: 14px 20px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
}

.newsletter-input:focus {
    outline: none;
    background: white;
}

.newsletter-btn {
    padding: 14px 24px;
    background: #1a202c;
    color: white;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
}

.newsletter-btn:hover {
    background: #2d3748;
    transform: translateY(-2px);
}

.footer-content {
    padding: 80px 0 40px;
}

.footer-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    gap: 40px;
}

.footer-column h4 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 20px;
    color: white;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.footer-logo-img {
    height: 40px;
    width: auto;
}

.footer-logo h3 {
    font-size: 20px;
    font-weight: 700;
    color: white;
}

.footer-description {
    color: #cbd5e0;
    line-height: 1.6;
    margin-bottom: 30px;
}

.footer-social h4 {
    font-size: 16px;
    margin-bottom: 16px;
}

.footer-social .social-links {
    display: flex;
    gap: 12px;
}

.footer-social .social-link {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.2s;
    text-decoration: none;
}

.footer-social .social-link.instagram {
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
}

.footer-social .social-link.twitter {
    background: #1da1f2;
}

.footer-social .social-link.facebook {
    background: #1877f2;
}

.footer-social .social-link.whatsapp {
    background: #25d366;
}

.footer-social .social-link.youtube {
    background: #ff0000;
}

.footer-social .social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: #cbd5e0;
    text-decoration: none;
    transition: color 0.2s;
    font-size: 14px;
}

.footer-links a:hover {
    color: white;
}

.contact-info {
    color: #cbd5e0;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
}

.contact-item i {
    color: #6366f1;
    margin-top: 2px;
    width: 16px;
}

.contact-text strong {
    color: white;
    display: block;
    margin-bottom: 4px;
}

.payment-section {
    background: #2d3748;
    padding: 30px 0;
}

.payment-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;
}

.payment-text h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
}

.payment-text p {
    color: #cbd5e0;
    font-size: 14px;
}

.payment-methods {
    display: flex;
    gap: 16px;
    align-items: center;
}

.payment-method {
    background: white;
    padding: 8px 12px;
    border-radius: 8px;
    height: 40px;
    display: flex;
    align-items: center;
}

.payment-method img {
    height: 24px;
    width: auto;
}

.footer-bottom {
    background: #1a202c;
    padding: 20px 0;
    border-top: 1px solid #374151;
}

.footer-bottom-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.copyright {
    color: #9ca3af;
    font-size: 14px;
}

.footer-bottom-links {
    display: flex;
    gap: 24px;
}

.footer-bottom-links a {
    color: #9ca3af;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s;
}

.footer-bottom-links a:hover {
    color: white;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    transition: all 0.2s;
    z-index: 1000;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    padding: 20px;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.modal-content {
    background: white;
    border-radius: 16px;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    z-index: 1;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1a202c;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #9ca3af;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 24px;
}

/* Loading Spinner */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.spinner {
    display: flex;
    gap: 4px;
}

.spinner > div {
    width: 12px;
    height: 12px;
    background: #6366f1;
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.spinner .bounce1 {
    animation-delay: -0.32s;
}

.spinner .bounce2 {
    animation-delay: -0.16s;
}

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 4000;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.toast {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px;
    min-width: 300px;
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(100%);
    animation: slideIn 0.3s ease-out forwards;
}

.toast-success {
    border-right: 4px solid #10b981;
}

.toast-error {
    border-right: 4px solid #ef4444;
}

.toast-info {
    border-right: 4px solid #6366f1;
}

.toast-content {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
}

.toast-content i {
    font-size: 16px;
}

.toast-success .toast-content i {
    color: #10b981;
}

.toast-error .toast-content i {
    color: #ef4444;
}

.toast-info .toast-content i {
    color: #6366f1;
}

.toast-close {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
}

.toast-close:hover {
    background: #f3f4f6;
    color: #374151;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .top-bar {
        display: none;
    }

    .header-content {
        gap: 8px;
        justify-content: space-between;
        flex-wrap: nowrap;
    }

    .header-search {
        display: none;
    }

    .header-actions {
        gap: 8px;
        flex-shrink: 0;
    }

    .header-action .action-text {
        display: none;
    }

    .header-logo {
        flex-shrink: 0;
        max-width: 120px;
    }

    .logo-img {
        max-width: 100%;
        height: auto;
    }

    /* Prevent horizontal scroll */
    body {
        overflow-x: hidden;
        max-width: 100vw;
    }

    .main-header {
        width: 100%;
        overflow: hidden;
    }

    /* Fix any elements that might cause overflow */
    img {
        max-width: 100%;
        height: auto;
    }

    .container {
        max-width: 100%;
        overflow: hidden;
    }

    .action-text {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .main-navigation {
        display: none;
    }

    .container {
        padding: 0 16px;
    }

    .hero-text h1 {
        font-size: 32px;
    }

    .hero-text p {
        font-size: 16px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .section-header h2 {
        font-size: 28px;
    }

    .categories-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }

    .products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .newsletter-content {
        flex-direction: column;
        text-align: center;
        gap: 30px;
    }

    .newsletter-form .form-group {
        flex-direction: column;
    }

    .footer-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .payment-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .payment-methods {
        justify-content: center;
        flex-wrap: wrap;
    }

    .footer-bottom-content {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .back-to-top {
        bottom: 20px;
        left: 20px;
        width: 45px;
        height: 45px;
    }

    .toast-container {
        right: 10px;
        left: 10px;
    }

    .toast {
        min-width: auto;
    }
}
