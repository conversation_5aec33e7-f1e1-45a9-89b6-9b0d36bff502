<?php
require_once 'config/database.php';
require_once 'includes/Product.php';
require_once 'includes/Category.php';
require_once 'includes/Cart.php';

// إنشاء كائنات الكلاسات
$product = new Product();
$category = new Category();
$cart = new Cart();

// الحصول على slug القسم
$category_slug = $_GET['slug'] ?? '';

if (empty($category_slug)) {
    header('Location: shein-index.php');
    exit;
}

// جلب معلومات القسم
$category_info = $category->get_by_slug($category_slug);
if (!$category_info) {
    header('Location: shein-index.php');
    exit;
}

$page_title = $category_info['name'] . ' - SHEIN Style';

// جلب منتجات القسم
$products_stmt = $product->read_by_category($category_info['id']);
$products = $products_stmt->fetchAll(PDO::FETCH_ASSOC);

// عدد العناصر في السلة
$cart_count = $cart->get_cart_count();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="css/shein-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body>

<!-- Header -->
<header class="shein-header">
    <div class="header-top">
        <div class="container">
            شحن مجاني للطلبات فوق 200 ريال | خصم 20% على أول طلب
        </div>
    </div>
    
    <div class="header-main">
        <div class="container">
            <div class="header-content">
                <a href="shein-index.php" class="logo">SHEIN</a>
                
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="ابحثي عن فساتين السهرة...">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                
                <div class="header-actions">
                    <a href="#" class="header-action">
                        <i class="fas fa-user"></i>
                    </a>
                    <a href="#" class="header-action">
                        <i class="fas fa-heart"></i>
                    </a>
                    <a href="shein-cart.php" class="header-action">
                        <i class="fas fa-shopping-bag"></i>
                        <?php if ($cart_count > 0): ?>
                        <span class="cart-count"><?php echo $cart_count; ?></span>
                        <?php endif; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Navigation -->
<nav class="shein-nav">
    <div class="container">
        <ul class="nav-links">
            <li><a href="shein-index.php" class="nav-link">الرئيسية</a></li>
            <li><a href="#" class="nav-link">فساتين سهرة</a></li>
            <li><a href="#" class="nav-link">فساتين كاجوال</a></li>
            <li><a href="#" class="nav-link">فساتين رسمية</a></li>
            <li><a href="#" class="nav-link">أحذية</a></li>
            <li><a href="#" class="nav-link">إكسسوارات</a></li>
            <li><a href="#" class="nav-link">تخفيضات</a></li>
        </ul>
    </div>
</nav>

<!-- Breadcrumb -->
<div class="breadcrumb">
    <div class="container">
        <a href="shein-index.php">الرئيسية</a>
        <span>/</span>
        <span><?php echo htmlspecialchars($category_info['name']); ?></span>
    </div>
</div>

<!-- Category Header -->
<section class="category-header">
    <div class="container">
        <h1><?php echo htmlspecialchars($category_info['name']); ?></h1>
        <?php if ($category_info['description']): ?>
        <p><?php echo htmlspecialchars($category_info['description']); ?></p>
        <?php endif; ?>
        <div class="products-count"><?php echo count($products); ?> منتج</div>
    </div>
</section>

<!-- Filter Bar -->
<div class="filter-bar">
    <div class="container">
        <div class="filter-content">
            <div class="filter-left">
                <button class="filter-btn">
                    <i class="fas fa-filter"></i>
                    فلترة
                </button>
                <button class="sort-btn">
                    <i class="fas fa-sort"></i>
                    ترتيب حسب
                </button>
            </div>
            <div class="view-toggle">
                <button class="view-btn active" data-view="grid">
                    <i class="fas fa-th"></i>
                </button>
                <button class="view-btn" data-view="list">
                    <i class="fas fa-list"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Products -->
<section class="shein-products">
    <div class="container">
        <?php if (!empty($products)): ?>
        <div class="products-grid" id="products-container">
            <?php foreach ($products as $prod): ?>
            <div class="shein-product-card">
                <div class="product-image">
                    <?php if ($prod['featured_image']): ?>
                        <img src="<?php echo UPLOAD_URL . $prod['featured_image']; ?>" alt="<?php echo htmlspecialchars($prod['title']); ?>" loading="lazy">
                    <?php else: ?>
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                            <i class="fas fa-image" style="font-size: 48px;"></i>
                        </div>
                    <?php endif; ?>
                    
                    <div class="product-badges">
                        <?php if ($prod['is_featured']): ?>
                            <span class="badge new">مميز</span>
                        <?php endif; ?>
                        
                        <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                            <span class="badge sale">خصم</span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="product-actions">
                        <button class="action-btn" title="عرض سريع">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn" title="إضافة للمفضلة">
                            <i class="fas fa-heart"></i>
                        </button>
                    </div>
                </div>
                
                <div class="product-info">
                    <h3 class="product-title">
                        <a href="shein-product.php?slug=<?php echo $prod['slug']; ?>" style="text-decoration: none; color: inherit;">
                            <?php echo htmlspecialchars($prod['title']); ?>
                        </a>
                    </h3>
                    
                    <div class="product-price">
                        <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                            <span class="current-price"><?php echo format_price($prod['sale_price']); ?></span>
                            <span class="original-price"><?php echo format_price($prod['price']); ?></span>
                        <?php else: ?>
                            <span class="current-price"><?php echo format_price($prod['price']); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($prod['stock_status'] == 'in_stock'): ?>
                    <button class="add-to-cart" data-product-id="<?php echo $prod['id']; ?>">
                        أضيفي للسلة
                    </button>
                    <?php else: ?>
                    <button class="add-to-cart" disabled style="background: #ccc; cursor: not-allowed;">
                        نفذ من المخزن
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php else: ?>
        <div class="no-products">
            <i class="fas fa-shopping-bag" style="font-size: 64px; color: #ccc; margin-bottom: 20px;"></i>
            <h3>لا توجد منتجات في هذا القسم</h3>
            <p>تصفحي الأقسام الأخرى أو عودي لاحقاً</p>
            <a href="shein-index.php" class="shein-btn">العودة للرئيسية</a>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Footer -->
<footer style="background: #000; color: #fff; padding: 40px 0; text-align: center;">
    <div class="container">
        <div style="margin-bottom: 20px;">
            <h3 style="font-size: 24px; font-weight: 900; margin-bottom: 10px;">SHEIN</h3>
            <p style="color: #ccc;">أجمل فساتين السهرة بأسعار لا تقاوم</p>
        </div>
        
        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <a href="#" style="color: #ccc; text-decoration: none;">من نحن</a>
            <a href="#" style="color: #ccc; text-decoration: none;">اتصل بنا</a>
            <a href="#" style="color: #ccc; text-decoration: none;">الشحن والإرجاع</a>
            <a href="#" style="color: #ccc; text-decoration: none;">الخصوصية</a>
        </div>
        
        <p style="color: #666; font-size: 12px;">© 2025 SHEIN Style. جميع الحقوق محفوظة.</p>
    </div>
</footer>

<script>
// Add to cart functionality
document.querySelectorAll('.add-to-cart').forEach(button => {
    button.addEventListener('click', function() {
        const productId = this.getAttribute('data-product-id');
        if (productId) {
            this.textContent = 'تمت الإضافة!';
            this.style.background = '#2ed573';
            setTimeout(() => {
                this.textContent = 'أضيفي للسلة';
                this.style.background = '#000';
            }, 2000);
        }
    });
});
</script>

</body>
</html>
