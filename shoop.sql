-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 25, 2025 at 01:40 AM
-- Server version: 10.4.27-MariaDB
-- PHP Version: 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `evening_dresses_store`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_activity_log`
--

CREATE TABLE `admin_activity_log` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `action` varchar(100) NOT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_activity_log`
--

INSERT INTO `admin_activity_log` (`id`, `admin_id`, `action`, `details`, `ip_address`, `user_agent`, `created_at`) VALUES
(44, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:35:36'),
(45, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:35:41'),
(46, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:22'),
(47, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:37'),
(48, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:38'),
(49, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:38'),
(50, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:39'),
(51, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:49'),
(52, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:49'),
(53, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:50'),
(54, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:51'),
(78, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:01'),
(90, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:45'),
(91, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:45'),
(92, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:50'),
(93, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:51'),
(94, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:51'),
(95, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:51'),
(96, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:51'),
(117, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:43:13'),
(123, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:18:54'),
(124, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:19:01'),
(125, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:19:12'),
(126, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:25:19'),
(127, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:25:31'),
(128, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:25:36'),
(129, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:25:37'),
(130, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:25:46'),
(131, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:25:56'),
(132, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:27:22'),
(133, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:28:43'),
(134, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:29:40'),
(135, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:29:47');

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('admin','manager') DEFAULT 'admin',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  `last_activity` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `reset_token` varchar(255) DEFAULT NULL,
  `reset_token_expires` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `email`, `password`, `full_name`, `role`, `created_at`, `updated_at`, `last_login`, `last_activity`, `is_active`, `reset_token`, `reset_token_expires`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', '2025-07-24 02:51:39', '2025-07-24 20:29:47', '2025-07-24 20:18:54', '2025-07-24 20:29:47', 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `areas`
--

CREATE TABLE `areas` (
  `id` int(11) NOT NULL,
  `city_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `areas`
--

INSERT INTO `areas` (`id`, `city_id`, `name`, `is_active`) VALUES
(1, 1, 'العليا', 1),
(2, 1, 'الملز', 1),
(3, 1, 'النخيل', 1),
(4, 2, 'البلد', 1),
(5, 2, 'الروضة', 1),
(6, 4, 'دبي مارينا', 1),
(7, 4, 'وسط البلد', 1),
(8, 1, 'العليا', 1),
(9, 1, 'الملز', 1),
(10, 1, 'النخيل', 1),
(11, 2, 'البلد', 1),
(12, 2, 'الروضة', 1),
(13, 4, 'دبي مارينا', 1),
(14, 4, 'وسط البلد', 1),
(15, 1, 'العليا', 1),
(16, 1, 'الملز', 1),
(17, 1, 'النخيل', 1),
(18, 2, 'البلد', 1),
(19, 2, 'الروضة', 1),
(20, 4, 'دبي مارينا', 1),
(21, 4, 'وسط البلد', 1);

-- --------------------------------------------------------

--
-- Table structure for table `cart`
--

CREATE TABLE `cart` (
  `id` int(11) NOT NULL,
  `session_id` varchar(100) NOT NULL,
  `product_id` int(11) NOT NULL,
  `color_id` int(11) DEFAULT NULL,
  `size_id` int(11) DEFAULT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cart`
--

INSERT INTO `cart` (`id`, `session_id`, `product_id`, `color_id`, `size_id`, `quantity`, `created_at`, `updated_at`) VALUES
(32, 'que4qhst9vcvnqpqfhoeoauufb', 1, NULL, NULL, 1, '2025-07-24 05:30:42', '2025-07-24 05:30:42'),
(34, 'que4qhst9vcvnqpqfhoeoauufb', 2, 1, 1, 1, '2025-07-24 20:18:05', '2025-07-24 20:18:05'),
(43, 'dhhi63g1hvbrivamj5la0t0vkp', 18, NULL, 2, 1, '2025-07-24 22:10:52', '2025-07-24 22:10:52'),
(44, 'dhhi63g1hvbrivamj5la0t0vkp', 18, NULL, NULL, 1, '2025-07-24 22:25:38', '2025-07-24 22:25:38'),
(45, 'dhhi63g1hvbrivamj5la0t0vkp', 18, NULL, NULL, 1, '2025-07-24 22:25:38', '2025-07-24 22:25:38'),
(46, 'dhhi63g1hvbrivamj5la0t0vkp', 18, NULL, NULL, 1, '2025-07-24 22:42:01', '2025-07-24 22:42:01');

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `seo_title` varchar(200) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `seo_content` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `slug`, `seo_title`, `description`, `seo_content`, `image`, `parent_id`, `sort_order`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'فساتين سهرة', 'evening-dresses', 'فساتين سهرة أنيقة وراقية', 'مجموعة متنوعة من فساتين السهرة الأنيقة', NULL, NULL, NULL, 1, 1, '2025-07-24 02:51:39', '2025-07-24 02:51:39'),
(2, 'فساتين طويلة', 'long-dresses', 'فساتين سهرة طويلة', 'فساتين سهرة طويلة وأنيقة لجميع المناسبات', NULL, NULL, NULL, 2, 1, '2025-07-24 02:51:39', '2025-07-24 02:51:39'),
(3, 'فساتين قصيرة', 'short-dresses', 'فساتين سهرة قصيرة', 'فساتين سهرة قصيرة عصرية وجذابة', NULL, NULL, NULL, 3, 1, '2025-07-24 02:51:39', '2025-07-24 02:51:39'),
(9, 'فساتين مناسبات', 'فساتين مناسبات-1', 'احداث فساتين مناسبات فى السعودية', 'تسوقي احداث موديلات فساتين مناسبات', 'تسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسبات', 'category_1753397329_9446.jpg', NULL, 0, 1, '2025-07-24 22:31:09', '2025-07-24 22:48:49');

-- --------------------------------------------------------

--
-- Table structure for table `cities`
--

CREATE TABLE `cities` (
  `id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cities`
--

INSERT INTO `cities` (`id`, `country_id`, `name`, `is_active`) VALUES
(1, 1, 'الرياض', 1),
(2, 1, 'جدة', 1),
(3, 1, 'الدمام', 1),
(4, 2, 'دبي', 1),
(5, 2, 'أبوظبي', 1),
(6, 3, 'الكويت', 1),
(7, 1, 'الرياض', 1),
(8, 1, 'جدة', 1),
(9, 1, 'الدمام', 1),
(10, 2, 'دبي', 1),
(11, 2, 'أبوظبي', 1),
(12, 3, 'الكويت', 1),
(13, 1, 'الرياض', 1),
(14, 1, 'جدة', 1),
(15, 1, 'الدمام', 1),
(16, 2, 'دبي', 1),
(17, 2, 'أبوظبي', 1),
(18, 3, 'الكويت', 1);

-- --------------------------------------------------------

--
-- Table structure for table `colors`
--

CREATE TABLE `colors` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `hex_code` varchar(7) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `colors`
--

INSERT INTO `colors` (`id`, `name`, `hex_code`, `is_active`, `created_at`) VALUES
(1, 'أسود', '#000000', 0, '2025-07-24 02:51:39'),
(2, 'أبيض', '#FFFFFF', 1, '2025-07-24 02:51:39'),
(3, 'أحمر', '#FF0000', 0, '2025-07-24 02:51:39'),
(4, 'أزرق', '#0000FF', 0, '2025-07-24 02:51:39'),
(5, 'وردي', '#FFC0CB', 1, '2025-07-24 02:51:39'),
(6, 'ذهبي', '#FFD700', 0, '2025-07-24 02:51:39'),
(7, 'فضي', '#C0C0C0', 0, '2025-07-24 02:51:39'),
(8, 'أسود', '#000000', 1, '2025-07-24 03:02:11'),
(9, 'أبيض', '#FFFFFF', 0, '2025-07-24 03:02:11'),
(10, 'أحمر', '#FF0000', 0, '2025-07-24 03:02:11'),
(11, 'أزرق', '#0000FF', 0, '2025-07-24 03:02:11'),
(12, 'وردي', '#FFC0CB', 0, '2025-07-24 03:02:11'),
(13, 'ذهبي', '#FFD700', 0, '2025-07-24 03:02:11'),
(14, 'فضي', '#C0C0C0', 0, '2025-07-24 03:02:11'),
(15, 'أسود', '#000000', 0, '2025-07-24 03:02:38'),
(16, 'أبيض', '#FFFFFF', 0, '2025-07-24 03:02:38'),
(17, 'أحمر', '#FF0000', 0, '2025-07-24 03:02:38'),
(18, 'أزرق', '#0000FF', 0, '2025-07-24 03:02:38'),
(19, 'وردي', '#FFC0CB', 1, '2025-07-24 03:02:38'),
(20, 'ذهبي', '#FFD700', 0, '2025-07-24 03:02:38'),
(21, 'فضي', '#C0C0C0', 0, '2025-07-24 03:02:38');

-- --------------------------------------------------------

--
-- Table structure for table `countries`
--

CREATE TABLE `countries` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `code` varchar(3) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `countries`
--

INSERT INTO `countries` (`id`, `name`, `code`, `is_active`) VALUES
(1, 'السعودية', 'SA', 1),
(2, 'الإمارات', 'AE', 1),
(3, 'الكويت', 'KW', 1),
(4, 'السعودية', 'SA', 1),
(5, 'الإمارات', 'AE', 1),
(6, 'الكويت', 'KW', 1),
(7, 'السعودية', 'SA', 1),
(8, 'الإمارات', 'AE', 1),
(9, 'الكويت', 'KW', 1);

-- --------------------------------------------------------

--
-- Table structure for table `coupons`
--

CREATE TABLE `coupons` (
  `id` int(11) NOT NULL,
  `code` varchar(50) NOT NULL,
  `type` enum('fixed','percentage') NOT NULL DEFAULT 'fixed',
  `value` decimal(10,2) NOT NULL,
  `minimum_amount` decimal(10,2) DEFAULT NULL,
  `maximum_discount` decimal(10,2) DEFAULT NULL,
  `usage_limit` int(11) DEFAULT NULL,
  `used_count` int(11) DEFAULT 0,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `coupon_usage`
--

CREATE TABLE `coupon_usage` (
  `id` int(11) NOT NULL,
  `coupon_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `customer_email` varchar(100) NOT NULL,
  `discount_amount` decimal(10,2) NOT NULL,
  `used_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `id` int(11) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customer_addresses`
--

CREATE TABLE `customer_addresses` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `area_id` int(11) NOT NULL,
  `address_line` varchar(255) NOT NULL,
  `is_default` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `type` enum('order','product','system','customer') NOT NULL,
  `title` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`data`)),
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` int(11) NOT NULL,
  `order_number` varchar(20) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `customer_name` varchar(100) NOT NULL,
  `customer_email` varchar(100) NOT NULL,
  `customer_phone` varchar(20) NOT NULL,
  `customer_phone_alt` varchar(20) DEFAULT NULL,
  `city_id` int(11) NOT NULL,
  `area_id` int(11) NOT NULL,
  `shipping_address` text NOT NULL,
  `landmark` varchar(255) DEFAULT NULL,
  `delivery_time` varchar(50) DEFAULT NULL,
  `delivery_date` date DEFAULT NULL,
  `shipping_company_id` int(11) DEFAULT NULL,
  `coupon_id` int(11) DEFAULT NULL,
  `coupon_code` varchar(50) DEFAULT NULL,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `subtotal` decimal(10,2) NOT NULL,
  `shipping_cost` decimal(10,2) NOT NULL,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL,
  `payment_method` enum('cod','bank_transfer','online') NOT NULL DEFAULT 'cod',
  `status` enum('pending','confirmed','processing','shipped','delivered','cancelled') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `orders`
--

INSERT INTO `orders` (`id`, `order_number`, `customer_id`, `customer_name`, `customer_email`, `customer_phone`, `customer_phone_alt`, `city_id`, `area_id`, `shipping_address`, `landmark`, `delivery_time`, `delivery_date`, `shipping_company_id`, `coupon_id`, `coupon_code`, `discount_amount`, `subtotal`, `shipping_cost`, `tax_amount`, `total_amount`, `payment_method`, `status`, `notes`, `created_at`, `updated_at`) VALUES
(1, 'ORD-********-1104', NULL, 'شسيشسيسش', '<EMAIL>', '**********', '**********', 1, 2, 'شسيسش', '', '', '0000-00-00', NULL, NULL, NULL, '0.00', '999.00', '0.00', '149.85', '1148.85', 'cod', 'pending', 'شيسبيسب', '2025-07-24 04:12:59', '2025-07-24 04:12:59');

-- --------------------------------------------------------

--
-- Table structure for table `order_items`
--

CREATE TABLE `order_items` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `color_id` int(11) DEFAULT NULL,
  `size_id` int(11) DEFAULT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `price` decimal(10,2) NOT NULL,
  `total` decimal(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `slug` varchar(200) NOT NULL,
  `seo_title` varchar(200) DEFAULT NULL,
  `seo_description` text DEFAULT NULL,
  `meta_keywords` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `sale_price` decimal(10,2) DEFAULT NULL,
  `category_id` int(11) NOT NULL,
  `stock_status` enum('in_stock','out_of_stock') DEFAULT 'in_stock',
  `featured_image` varchar(255) DEFAULT NULL,
  `video_url` varchar(500) DEFAULT NULL,
  `video_file` varchar(255) DEFAULT NULL,
  `is_featured` tinyint(1) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `views_count` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `products`
--

INSERT INTO `products` (`id`, `title`, `slug`, `seo_title`, `seo_description`, `meta_keywords`, `description`, `price`, `sale_price`, `category_id`, `stock_status`, `featured_image`, `video_url`, `video_file`, `is_featured`, `is_active`, `views_count`, `created_at`, `updated_at`) VALUES
(1, 'فستان سهرة أنيق باللون الأسود', 'elegant-black-evening-dress', NULL, NULL, NULL, 'فستان سهرة راقي وأنيق باللون الأسود، مصنوع من أجود الأقمشة. مناسب لجميع المناسبات الخاصة والحفلات. يتميز بتصميم عصري وقصة مثالية تناسب جميع أشكال الجسم.', '899.00', '699.00', 1, 'in_stock', 'products/dress1.jpg', NULL, NULL, 1, 0, 35, '2025-07-24 02:51:39', '2025-07-24 21:06:59'),
(2, 'فستان سهرة طويل بالترتر الذهبي', 'long-golden-sequin-evening-dress', NULL, NULL, NULL, 'فستان سهرة طويل مزين بالترتر الذهبي اللامع. تصميم فاخر ومميز يجعلك تتألقين في أي مناسبة. مصنوع من أقمشة عالية الجودة مع بطانة مريحة.', '1299.00', '999.00', 2, 'in_stock', 'products/dress2.jpg', NULL, NULL, 1, 0, 16, '2025-07-24 02:51:39', '2025-07-24 21:07:01'),
(3, 'فستان سهرة قصير عصري', 'modern-short-evening-dress', NULL, NULL, NULL, 'فستان سهرة قصير بتصميم عصري وجذاب. مثالي للحفلات والمناسبات الخاصة. يتميز بقصة أنيقة وألوان متنوعة تناسب جميع الأذواق.', '599.00', '499.00', 3, 'in_stock', 'products/dress3.jpg', NULL, NULL, 0, 0, 0, '2025-07-24 02:51:39', '2025-07-24 21:07:03'),
(4, 'فستان سهرة كلاسيكي أزرق', 'classic-blue-evening-dress', NULL, NULL, NULL, 'فستان سهرة كلاسيكي باللون الأزرق الملكي. تصميم خالد وأنيق يناسب جميع المناسبات الرسمية. مصنوع من الحرير الطبيعي عالي الجودة.', '799.00', NULL, 1, 'in_stock', 'products/dress4.jpg', NULL, NULL, 1, 0, 1, '2025-07-24 02:51:39', '2025-07-24 21:07:05'),
(5, 'فستان سهرة وردي بالدانتيل', 'pink-lace-evening-dress', NULL, NULL, NULL, 'فستان سهرة رومانسي باللون الوردي مزين بالدانتيل الفرنسي. مثالي للمناسبات الخاصة والحفلات الرومانسية. تصميم أنثوي وناعم.', '950.00', '750.00', 2, 'in_stock', 'products/dress5.jpg', NULL, NULL, 1, 0, 3, '2025-07-24 02:51:39', '2025-07-24 21:07:06'),
(12, 'fdazsdasdsad', 'sh', NULL, NULL, NULL, 'asdasdas', '600.00', '0.00', 1, 'in_stock', '6881c7909abb1.jpg', '', NULL, 0, 0, 0, '2025-07-24 05:41:36', '2025-07-24 21:06:58'),
(13, 'fdazsdasdsad', 'sh-1753335705', NULL, NULL, NULL, 'asdasdas', '600.00', NULL, 1, 'in_stock', '6881c799e7cea.jpg', '', NULL, 0, 0, 7, '2025-07-24 05:41:45', '2025-07-24 21:06:56'),
(14, 'فساتين سهرة رائع', 'asddsadas', NULL, NULL, NULL, 'فساتين سهرة رائع', '400.00', '350.00', 1, 'in_stock', 'products/product_14_1753391308_0.jpg', '', NULL, 0, 1, 81, '2025-07-24 21:08:05', '2025-07-24 21:43:36'),
(15, 'فستان سهره اصفر', 'fstan-shrh-asfr', NULL, NULL, NULL, 'فستان سهره اصفر', '500.00', '450.00', 1, 'in_stock', 'products/product_15_1753393255_0.jpg', '', NULL, 0, 1, 63, '2025-07-24 21:23:59', '2025-07-24 23:16:20'),
(17, 'فستان احمر رائع جدا', 'fstan-ahmr-raa-jda', NULL, NULL, NULL, 'فستان احمر رائع جدا', '600.00', '550.00', 1, 'in_stock', 'products/product_17_1753394555_0.jpg', '', 'videos/video_17_1753394555.mp4', 0, 0, 1, '2025-07-24 22:02:35', '2025-07-24 22:05:02'),
(18, 'فستان سهرة احمر وردي', 'fstan-shrh-ahmr-wrdy', NULL, NULL, NULL, 'فستان سهرة احمر وردي', '400.00', '350.00', 9, 'in_stock', 'products/product_18_1753394584_0.jpg', '', 'videos/video_18_1753395780.mp4', 0, 1, 55, '2025-07-24 22:03:04', '2025-07-24 23:34:33');

-- --------------------------------------------------------

--
-- Table structure for table `product_colors`
--

CREATE TABLE `product_colors` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `color_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_colors`
--

INSERT INTO `product_colors` (`id`, `product_id`, `color_id`) VALUES
(1, 1, 1),
(2, 1, 2),
(3, 1, 3),
(4, 1, 5),
(7, 2, 1),
(5, 2, 6),
(6, 2, 7),
(8, 3, 2),
(9, 3, 3),
(10, 3, 4),
(11, 3, 5),
(13, 4, 1),
(14, 4, 2),
(12, 4, 4),
(16, 5, 2),
(15, 5, 5),
(17, 5, 7),
(18, 12, 3),
(22, 12, 5),
(19, 12, 15),
(20, 12, 20),
(21, 12, 21);

-- --------------------------------------------------------

--
-- Table structure for table `product_images`
--

CREATE TABLE `product_images` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `image_type` enum('gallery','lifestyle','main') DEFAULT 'gallery',
  `sort_order` int(11) DEFAULT 0,
  `alt_text` varchar(200) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_images`
--

INSERT INTO `product_images` (`id`, `product_id`, `image_path`, `image_type`, `sort_order`, `alt_text`, `created_at`) VALUES
(1, 12, '6881c790a3352.jpg', 'gallery', 0, NULL, '2025-07-24 05:41:36'),
(2, 12, '6881c790a4198.jpg', 'lifestyle', 0, NULL, '2025-07-24 05:41:36'),
(3, 13, '6881c799ed11c.jpg', 'gallery', 0, NULL, '2025-07-24 05:41:45'),
(4, 13, '6881c799ed867.jpg', 'lifestyle', 0, NULL, '2025-07-24 05:41:45'),
(5, 13, 'products/product_13_1753391122_0.jpg', 'gallery', 1, 'صورة المنتج 1', '2025-07-24 21:05:22'),
(6, 13, 'products/product_13_1753391122_1.jpg', 'gallery', 2, 'صورة المنتج 2', '2025-07-24 21:05:22'),
(7, 13, 'products/product_13_1753391122_2.jpg', 'gallery', 3, 'صورة المنتج 3', '2025-07-24 21:05:22'),
(8, 13, 'products/product_13_1753391179_0.jpg', 'lifestyle', 1, 'صورة المنتج 1', '2025-07-24 21:06:19'),
(9, 13, 'products/product_13_1753391179_1.jpg', 'lifestyle', 2, 'صورة المنتج 2', '2025-07-24 21:06:19'),
(10, 14, 'products/product_14_1753391308_0.jpg', 'gallery', 1, 'صورة المنتج 1', '2025-07-24 21:08:29'),
(11, 14, 'products/product_14_1753391309_1.jpg', 'gallery', 2, 'صورة المنتج 2', '2025-07-24 21:08:29'),
(14, 15, 'products/product_15_1753393255_0.jpg', 'gallery', 1, 'صورة المنتج 1', '2025-07-24 21:40:55'),
(15, 15, 'products/product_15_1753393486_0.jpg', 'lifestyle', 2, 'صورة المنتج 1', '2025-07-24 21:44:47'),
(18, 17, 'products/product_17_1753394555_0.jpg', 'gallery', 1, 'صورة المنتج 1', '2025-07-24 22:02:35'),
(19, 17, 'products/product_17_1753394555_0.jpg', 'lifestyle', 1, 'صورة المنتج 1', '2025-07-24 22:02:35'),
(20, 18, 'products/product_18_1753394584_0.jpg', 'gallery', 1, 'صورة المنتج 1', '2025-07-24 22:03:04'),
(21, 18, 'products/product_18_1753394584_0.jpg', 'lifestyle', 1, 'صورة المنتج 1', '2025-07-24 22:03:04'),
(22, 18, 'products/product_18_1753395244_0.jpg', 'gallery', 2, 'صورة المنتج 1', '2025-07-24 22:14:04');

-- --------------------------------------------------------

--
-- Table structure for table `product_reviews`
--

CREATE TABLE `product_reviews` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `customer_name` varchar(100) NOT NULL,
  `customer_email` varchar(100) NOT NULL,
  `rating` tinyint(1) NOT NULL CHECK (`rating` >= 1 and `rating` <= 5),
  `review_text` text DEFAULT NULL,
  `is_approved` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `product_sizes`
--

CREATE TABLE `product_sizes` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `size_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_sizes`
--

INSERT INTO `product_sizes` (`id`, `product_id`, `size_id`) VALUES
(1, 1, 1),
(2, 1, 2),
(3, 1, 3),
(4, 1, 4),
(5, 1, 5),
(6, 1, 6),
(7, 2, 1),
(8, 2, 2),
(9, 2, 3),
(10, 2, 4),
(11, 2, 5),
(12, 2, 6),
(13, 3, 2),
(14, 3, 3),
(15, 3, 4),
(16, 3, 5),
(17, 4, 1),
(18, 4, 2),
(19, 4, 3),
(20, 4, 4),
(21, 4, 5),
(22, 5, 1),
(23, 5, 2),
(24, 5, 3),
(25, 5, 4),
(26, 5, 5),
(27, 5, 6),
(28, 12, 1),
(30, 12, 2),
(36, 13, 4),
(37, 13, 5),
(38, 13, 6),
(39, 14, 1),
(40, 14, 2),
(41, 14, 3),
(42, 14, 4),
(43, 15, 1),
(44, 15, 2),
(45, 15, 3),
(46, 15, 4),
(53, 17, 1),
(54, 17, 2),
(55, 17, 3),
(56, 17, 4),
(57, 17, 5),
(58, 17, 6),
(115, 18, 1),
(116, 18, 2),
(117, 18, 3),
(118, 18, 4);

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
(1, 'site_logo', 'logo.png', '2025-07-24 23:14:18', '2025-07-24 23:14:18'),
(2, 'site_name', 'فساتين شوب', '2025-07-24 23:14:36', '2025-07-24 23:17:02'),
(3, 'site_description', 'أجمل وأحدث تصاميم فساتين السهرة الأنيقة والراقية', '2025-07-24 23:14:36', '2025-07-24 23:17:02'),
(4, 'site_keywords', 'فساتين سهرة, فساتين أنيقة, فساتين راقية', '2025-07-24 23:14:36', '2025-07-24 23:17:03'),
(5, 'contact_email', '', '2025-07-24 23:14:36', '2025-07-24 23:17:03'),
(6, 'contact_phone', '', '2025-07-24 23:14:36', '2025-07-24 23:17:03'),
(7, 'facebook_url', '', '2025-07-24 23:14:36', '2025-07-24 23:17:03'),
(8, 'instagram_url', '', '2025-07-24 23:14:36', '2025-07-24 23:17:03'),
(9, 'twitter_url', '', '2025-07-24 23:14:36', '2025-07-24 23:17:03');

-- --------------------------------------------------------

--
-- Table structure for table `shipping_companies`
--

CREATE TABLE `shipping_companies` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `cost` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `shipping_companies`
--

INSERT INTO `shipping_companies` (`id`, `name`, `logo`, `cost`, `is_active`, `created_at`) VALUES
(1, 'سمسا', 'fast_shipping.png', '50.00', 1, '2025-07-24 02:51:39'),
(2, 'البريد السعودي', 'guaranteed_delivery.png', '100.00', 1, '2025-07-24 02:51:39');

-- --------------------------------------------------------

--
-- Table structure for table `site_settings`
--

CREATE TABLE `site_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('text','textarea','number','boolean','json') DEFAULT 'text',
  `category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `site_settings`
--

INSERT INTO `site_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `category`, `description`, `created_at`, `updated_at`) VALUES
(1, 'site_name', 'متجر فساتين السهرة', 'text', 'general', 'اسم الموقع', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(2, 'site_description', 'متجر متخصص في فساتين السهرة الأنيقة والراقية', 'textarea', 'general', 'وصف الموقع', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(3, 'site_keywords', 'فساتين سهرة, فساتين أنيقة, فساتين راقية', 'textarea', 'seo', 'الكلمات المفتاحية', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(4, 'contact_email', '<EMAIL>', 'text', 'contact', 'البريد الإلكتروني للتواصل', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(5, 'contact_phone', '+966500000000', 'text', 'contact', 'رقم الهاتف', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(6, 'free_shipping_threshold', '500', 'number', 'shipping', 'الحد الأدنى للشحن المجاني', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(7, 'tax_rate', '0.15', 'number', 'general', 'معدل الضريبة', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(8, 'currency_symbol', 'ر.س', 'text', 'general', 'رمز العملة', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(9, 'items_per_page', '12', 'number', 'general', 'عدد العناصر في الصفحة', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(10, 'maintenance_mode', '0', 'boolean', 'general', 'وضع الصيانة', '2025-07-24 04:25:02', '2025-07-24 04:25:02');

-- --------------------------------------------------------

--
-- Table structure for table `sizes`
--

CREATE TABLE `sizes` (
  `id` int(11) NOT NULL,
  `name` varchar(20) NOT NULL,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `sizes`
--

INSERT INTO `sizes` (`id`, `name`, `sort_order`, `is_active`, `created_at`) VALUES
(1, 'XS', 1, 1, '2025-07-24 02:51:39'),
(2, 'S', 2, 1, '2025-07-24 02:51:39'),
(3, 'M', 3, 1, '2025-07-24 02:51:39'),
(4, 'L', 4, 1, '2025-07-24 02:51:39'),
(5, 'XL', 5, 1, '2025-07-24 02:51:39'),
(6, 'XXL', 6, 1, '2025-07-24 02:51:39');

-- --------------------------------------------------------

--
-- Table structure for table `wishlist`
--

CREATE TABLE `wishlist` (
  `id` int(11) NOT NULL,
  `session_id` varchar(100) DEFAULT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `product_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `action` (`action`),
  ADD KEY `created_at` (`created_at`);

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `areas`
--
ALTER TABLE `areas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `city_id` (`city_id`);

--
-- Indexes for table `cart`
--
ALTER TABLE `cart`
  ADD PRIMARY KEY (`id`),
  ADD KEY `color_id` (`color_id`),
  ADD KEY `size_id` (`size_id`),
  ADD KEY `idx_session_id` (`session_id`),
  ADD KEY `idx_product_id` (`product_id`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `parent_id` (`parent_id`);

--
-- Indexes for table `cities`
--
ALTER TABLE `cities`
  ADD PRIMARY KEY (`id`),
  ADD KEY `country_id` (`country_id`);

--
-- Indexes for table `colors`
--
ALTER TABLE `colors`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `countries`
--
ALTER TABLE `countries`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `coupons`
--
ALTER TABLE `coupons`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `coupon_usage`
--
ALTER TABLE `coupon_usage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `coupon_id` (`coupon_id`),
  ADD KEY `order_id` (`order_id`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `customer_addresses`
--
ALTER TABLE `customer_addresses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `area_id` (`area_id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `type` (`type`),
  ADD KEY `is_read` (`is_read`),
  ADD KEY `created_at` (`created_at`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `order_number` (`order_number`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `city_id` (`city_id`),
  ADD KEY `area_id` (`area_id`),
  ADD KEY `shipping_company_id` (`shipping_company_id`),
  ADD KEY `coupon_id` (`coupon_id`);

--
-- Indexes for table `order_items`
--
ALTER TABLE `order_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `color_id` (`color_id`),
  ADD KEY `size_id` (`size_id`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `product_colors`
--
ALTER TABLE `product_colors`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_product_color` (`product_id`,`color_id`),
  ADD KEY `color_id` (`color_id`);

--
-- Indexes for table `product_images`
--
ALTER TABLE `product_images`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`);

--
-- Indexes for table `product_reviews`
--
ALTER TABLE `product_reviews`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `is_approved` (`is_approved`);

--
-- Indexes for table `product_sizes`
--
ALTER TABLE `product_sizes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_product_size` (`product_id`,`size_id`),
  ADD KEY `size_id` (`size_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `shipping_companies`
--
ALTER TABLE `shipping_companies`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `site_settings`
--
ALTER TABLE `site_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `sizes`
--
ALTER TABLE `sizes`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `wishlist`
--
ALTER TABLE `wishlist`
  ADD PRIMARY KEY (`id`),
  ADD KEY `session_id` (`session_id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `product_id` (`product_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=136;

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `areas`
--
ALTER TABLE `areas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `cart`
--
ALTER TABLE `cart`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=47;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `cities`
--
ALTER TABLE `cities`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `colors`
--
ALTER TABLE `colors`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `countries`
--
ALTER TABLE `countries`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `coupons`
--
ALTER TABLE `coupons`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `coupon_usage`
--
ALTER TABLE `coupon_usage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `customer_addresses`
--
ALTER TABLE `customer_addresses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `order_items`
--
ALTER TABLE `order_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `product_colors`
--
ALTER TABLE `product_colors`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- AUTO_INCREMENT for table `product_images`
--
ALTER TABLE `product_images`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `product_reviews`
--
ALTER TABLE `product_reviews`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `product_sizes`
--
ALTER TABLE `product_sizes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=119;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `shipping_companies`
--
ALTER TABLE `shipping_companies`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `site_settings`
--
ALTER TABLE `site_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `sizes`
--
ALTER TABLE `sizes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `wishlist`
--
ALTER TABLE `wishlist`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  ADD CONSTRAINT `admin_activity_log_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `areas`
--
ALTER TABLE `areas`
  ADD CONSTRAINT `areas_ibfk_1` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `cart`
--
ALTER TABLE `cart`
  ADD CONSTRAINT `cart_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cart_ibfk_2` FOREIGN KEY (`color_id`) REFERENCES `colors` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `cart_ibfk_3` FOREIGN KEY (`size_id`) REFERENCES `sizes` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `categories`
--
ALTER TABLE `categories`
  ADD CONSTRAINT `categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `cities`
--
ALTER TABLE `cities`
  ADD CONSTRAINT `cities_ibfk_1` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `coupon_usage`
--
ALTER TABLE `coupon_usage`
  ADD CONSTRAINT `coupon_usage_ibfk_1` FOREIGN KEY (`coupon_id`) REFERENCES `coupons` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `coupon_usage_ibfk_2` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `customer_addresses`
--
ALTER TABLE `customer_addresses`
  ADD CONSTRAINT `customer_addresses_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_addresses_ibfk_2` FOREIGN KEY (`area_id`) REFERENCES `areas` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `orders_ibfk_3` FOREIGN KEY (`area_id`) REFERENCES `areas` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `orders_ibfk_4` FOREIGN KEY (`shipping_company_id`) REFERENCES `shipping_companies` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `orders_ibfk_5` FOREIGN KEY (`coupon_id`) REFERENCES `coupons` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `order_items`
--
ALTER TABLE `order_items`
  ADD CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `order_items_ibfk_3` FOREIGN KEY (`color_id`) REFERENCES `colors` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `order_items_ibfk_4` FOREIGN KEY (`size_id`) REFERENCES `sizes` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `products`
--
ALTER TABLE `products`
  ADD CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `product_colors`
--
ALTER TABLE `product_colors`
  ADD CONSTRAINT `product_colors_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `product_colors_ibfk_2` FOREIGN KEY (`color_id`) REFERENCES `colors` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `product_images`
--
ALTER TABLE `product_images`
  ADD CONSTRAINT `product_images_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `product_reviews`
--
ALTER TABLE `product_reviews`
  ADD CONSTRAINT `product_reviews_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `product_sizes`
--
ALTER TABLE `product_sizes`
  ADD CONSTRAINT `product_sizes_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `product_sizes_ibfk_2` FOREIGN KEY (`size_id`) REFERENCES `sizes` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `wishlist`
--
ALTER TABLE `wishlist`
  ADD CONSTRAINT `wishlist_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wishlist_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
