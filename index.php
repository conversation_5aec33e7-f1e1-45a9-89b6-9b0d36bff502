<?php
require_once 'config/database.php';
require_once 'includes/Product.php';
require_once 'includes/Category.php';
require_once 'includes/Cart.php';

$page_title = 'الصفحة الرئيسية';

// إنشاء كائنات الكلاسات
$product = new Product();
$category = new Category();
$cart = new Cart();

// جلب المنتجات المميزة
$featured_products_stmt = $product->read(8, 0, null, 1);
$featured_products = $featured_products_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب أحدث المنتجات
$latest_products_stmt = $product->read(8, 0);
$latest_products = $latest_products_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب الأقسام الرئيسية
$main_categories_stmt = $category->get_main_categories();
$main_categories = $main_categories_stmt->fetchAll(PDO::FETCH_ASSOC);

// عدد العناصر في السلة
$cart_count = $cart->get_cart_count();

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="hero-content">
            <div class="hero-text">
                <h1>أجمل فساتين السهرة</h1>
                <p>اكتشفي مجموعتنا الحصرية من فساتين السهرة الأنيقة والعصرية التي تناسب جميع المناسبات الخاصة</p>
                <div class="hero-buttons">
                    <a href="products.php" class="btn btn-primary">تسوقي الآن</a>
                    <a href="#categories" class="btn btn-outline">استكشفي الأقسام</a>
                </div>
            </div>
            <div class="hero-image">
                <img src="uploads/hero-dress.jpg" alt="فساتين سهرة أنيقة" onerror="this.style.display='none'">
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section id="categories" class="categories-section">
    <div class="container">
        <div class="section-header">
            <h2>تسوقي حسب القسم</h2>
            <p>اختاري من مجموعة متنوعة من فساتين السهرة الأنيقة والعصرية</p>
        </div>

        <div class="categories-grid">
            <?php foreach ($main_categories as $cat): ?>
            <a href="category.php?slug=<?php echo $cat['slug']; ?>" class="category-card">
                <div class="category-box">
                    <div class="category-image">
                        <?php if ($cat['image']): ?>
                            <img src="uploads/categories/<?php echo htmlspecialchars($cat['image']); ?>" alt="<?php echo htmlspecialchars($cat['name']); ?>">
                        <?php else: ?>
                            <div class="no-image">
                                <i class="fas fa-tags"></i>
                                <span><?php echo htmlspecialchars($cat['name']); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="category-overlay">
                        <div class="category-content">
                            <h3><?php echo htmlspecialchars($cat['name']); ?></h3>
                            <span class="category-link">تسوقي الآن <i class="fas fa-arrow-left"></i></span>
                        </div>
                    </div>
                </div>
            </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<?php if (!empty($featured_products)): ?>
<section class="featured-products-section">
    <div class="container">
        <div class="section-header">
            <h2>المنتجات المميزة</h2>
            <p>اختيارنا المميز من أجمل فساتين السهرة الأنيقة والعصرية</p>
        </div>

        <div class="products-grid">
            <?php foreach ($featured_products as $prod): ?>
            <div class="product-card">
                <div class="product-image">
                    <?php if ($prod['featured_image']): ?>
                        <img src="<?php echo UPLOAD_URL . $prod['featured_image']; ?>" alt="<?php echo htmlspecialchars($prod['title']); ?>" loading="lazy">
                    <?php else: ?>
                        <div class="no-image">
                            <i class="fas fa-image"></i>
                            <span>لا توجد صورة</span>
                        </div>
                    <?php endif; ?>

                    <div class="product-badges">
                        <?php if ($prod['is_featured']): ?>
                            <span class="badge featured">مميز</span>
                        <?php endif; ?>

                        <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                            <span class="badge sale">خصم</span>
                            <?php
                            $discount = round((($prod['price'] - $prod['sale_price']) / $prod['price']) * 100);
                            if ($discount > 0): ?>
                                <span class="badge discount">-<?php echo $discount; ?>%</span>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php if ($prod['stock_status'] == 'out_of_stock'): ?>
                            <span class="badge out-of-stock">نفذ</span>
                        <?php endif; ?>
                    </div>

                    <div class="product-actions">
                        <button class="action-btn quick-view" data-product-id="<?php echo $prod['id']; ?>" title="عرض سريع">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn wishlist" data-product-id="<?php echo $prod['id']; ?>" title="إضافة للمفضلة">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="action-btn compare" data-product-id="<?php echo $prod['id']; ?>" title="مقارنة">
                            <i class="fas fa-balance-scale"></i>
                        </button>
                    </div>
                </div>
                
                <div class="product-info">
                    <h3 class="product-title">
                        <a href="product.php?slug=<?php echo $prod['slug']; ?>"><?php echo htmlspecialchars($prod['title']); ?></a>
                    </h3>

                    <div class="product-rating">
                        <div class="stars">
                            <?php
                            $rating = rand(4, 5); // تقييم عشوائي للعرض
                            for ($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star<?php echo $i <= $rating ? '' : '-o'; ?>"></i>
                            <?php endfor; ?>
                        </div>
                        <span class="rating-count">(<?php echo rand(15, 85); ?> تقييم)</span>
                    </div>

                    <div class="product-meta">
                        <div class="stock-status <?php echo $prod['stock_status'] == 'in_stock' ? 'in-stock' : 'out-of-stock'; ?>">
                            <?php echo $prod['stock_status'] == 'in_stock' ? 'متوفر' : 'نفذ من المخزن'; ?>
                        </div>
                    </div>

                    <div class="product-price">
                        <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                            <span class="sale-price"><?php echo format_price($prod['sale_price']); ?></span>
                            <span class="original-price"><?php echo format_price($prod['price']); ?></span>
                            <?php
                            $discount = round((($prod['price'] - $prod['sale_price']) / $prod['price']) * 100);
                            if ($discount > 0): ?>
                                <span class="discount-percentage">-<?php echo $discount; ?>%</span>
                            <?php endif; ?>
                        <?php else: ?>
                            <span class="current-price"><?php echo format_price($prod['price']); ?></span>
                        <?php endif; ?>
                    </div>

                    <?php if ($prod['stock_status'] == 'in_stock'): ?>
                    <button class="add-to-cart" data-product-id="<?php echo $prod['id']; ?>">
                        <i class="fas fa-shopping-cart"></i>
                        أضيفي للسلة
                    </button>
                    <?php else: ?>
                    <button class="add-to-cart" disabled>
                        <i class="fas fa-times"></i>
                        نفذ من المخزن
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="section-footer">
            <a href="products.php?featured=1" class="btn btn-outline">عرض جميع المنتجات المميزة</a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Latest Products Section -->
<?php if (!empty($latest_products)): ?>
<section class="latest-products-section">
    <div class="container">
        <div class="section-header">
            <h2>أحدث المنتجات</h2>
            <p>آخر إضافاتنا من فساتين السهرة العصرية</p>
        </div>
        
        <div class="products-grid">
            <?php foreach ($latest_products as $prod): ?>
            <div class="product-card">
                <div class="product-image">
                    <?php if ($prod['featured_image']): ?>
                        <img src="<?php echo UPLOAD_URL . $prod['featured_image']; ?>" alt="<?php echo $prod['title']; ?>">
                    <?php else: ?>
                        <div class="no-image">
                            <i class="fas fa-image"></i>
                        </div>
                    <?php endif; ?>
                    
                    <div class="product-badges">
                        <span class="badge new">جديد</span>
                        
                        <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                            <span class="badge sale">خصم</span>
                        <?php endif; ?>
                        
                        <?php if ($prod['stock_status'] == 'out_of_stock'): ?>
                            <span class="badge out-of-stock">نفذ</span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="product-actions">
                        <button class="action-btn quick-view" data-product-id="<?php echo $prod['id']; ?>">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn add-to-wishlist" data-product-id="<?php echo $prod['id']; ?>">
                            <i class="fas fa-heart"></i>
                        </button>
                    </div>
                </div>
                
                <div class="product-info">
                    <h3 class="product-title">
                        <a href="product.php?slug=<?php echo $prod['slug']; ?>"><?php echo $prod['title']; ?></a>
                    </h3>
                    
                    <div class="product-price">
                        <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                            <span class="sale-price"><?php echo format_price($prod['sale_price']); ?></span>
                            <span class="original-price"><?php echo format_price($prod['price']); ?></span>
                        <?php else: ?>
                            <span class="current-price"><?php echo format_price($prod['price']); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="product-rating">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <span class="rating-count">(<?php echo rand(5, 30); ?>)</span>
                    </div>
                    
                    <?php if ($prod['stock_status'] == 'in_stock'): ?>
                    <button class="btn btn-primary add-to-cart" data-product-id="<?php echo $prod['id']; ?>">
                        <i class="fas fa-shopping-cart"></i>
                        أضيفي للسلة
                    </button>
                    <?php else: ?>
                    <button class="btn btn-disabled" disabled>
                        <i class="fas fa-times"></i>
                        نفذ من المخزن
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="section-footer">
            <a href="products.php" class="btn btn-outline">عرض جميع المنتجات</a>
        </div>
    </div>
</section>
<?php endif; ?>



<?php include 'includes/footer.php'; ?>
