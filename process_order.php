<?php
session_start();
require_once 'config/database.php';
require_once 'includes/Cart.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: cart.php');
    exit();
}

// التحقق من وجود عناصر في السلة
$cart = new Cart();
$cart_items = $cart->get_cart_items();

if (empty($cart_items)) {
    $_SESSION['error_message'] = 'السلة فارغة';
    header('Location: cart.php');
    exit();
}

// جمع البيانات من النموذج
$customer_name = sanitize_input($_POST['customer_name']);
$customer_email = sanitize_input($_POST['customer_email']);
$customer_phone = sanitize_input($_POST['customer_phone']);
$customer_phone_alt = isset($_POST['customer_phone_alt']) ? sanitize_input($_POST['customer_phone_alt']) : '';
$city_id = (int)sanitize_input($_POST['city_id']);
$area_id = (int)sanitize_input($_POST['area_id']);
$address = sanitize_input($_POST['address']);
$landmark = isset($_POST['landmark']) ? sanitize_input($_POST['landmark']) : '';
$delivery_time = isset($_POST['delivery_time']) ? sanitize_input($_POST['delivery_time']) : '';
$delivery_date = isset($_POST['delivery_date']) ? sanitize_input($_POST['delivery_date']) : '';
$notes = isset($_POST['notes']) ? sanitize_input($_POST['notes']) : '';
$payment_method = sanitize_input($_POST['payment_method']);

// التحقق من البيانات المطلوبة
$errors = [];

if (empty($customer_name)) {
    $errors[] = 'الاسم الكامل مطلوب';
}

if (empty($customer_email) || !filter_var($customer_email, FILTER_VALIDATE_EMAIL)) {
    $errors[] = 'البريد الإلكتروني غير صحيح';
}

if (empty($customer_phone)) {
    $errors[] = 'رقم الهاتف مطلوب';
}

if (empty($city_id)) {
    $errors[] = 'المدينة مطلوبة';
}

if (empty($area_id)) {
    $errors[] = 'المنطقة مطلوبة';
}

if (empty($address)) {
    $errors[] = 'العنوان التفصيلي مطلوب';
}

if (empty($payment_method)) {
    $errors[] = 'طريقة الدفع مطلوبة';
}

if (!isset($_POST['accept_terms'])) {
    $errors[] = 'يجب الموافقة على الشروط والأحكام';
}

// إذا كانت هناك أخطاء، العودة لصفحة الدفع
if (!empty($errors)) {
    $_SESSION['error_message'] = implode('<br>', $errors);
    $_SESSION['form_data'] = $_POST;
    header('Location: checkout.php');
    exit();
}

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // بدء المعاملة
    $conn->beginTransaction();
    
    // حساب الإجماليات
    $subtotal = 0;
    foreach ($cart_items as $item) {
        $price = ($item['sale_price'] && $item['sale_price'] > 0) ? $item['sale_price'] : $item['price'];
        $subtotal += $price * $item['quantity'];
    }

    $shipping_cost = $subtotal >= 500 ? 0 : 50; // شحن مجاني للطلبات أكثر من 500 ريال
    $tax_rate = 0.15; // ضريبة القيمة المضافة 15%
    $tax_amount = $subtotal * $tax_rate;
    $total = $subtotal + $shipping_cost + $tax_amount;
    
    // إنشاء رقم الطلب
    $order_number = 'ORD-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    // إدراج الطلب
    $order_query = "INSERT INTO orders (
        order_number, customer_name, customer_email, customer_phone, customer_phone_alt,
        city_id, area_id, shipping_address, landmark, delivery_time, delivery_date,
        subtotal, shipping_cost, tax_amount, total_amount, payment_method, notes,
        status, created_at
    ) VALUES (
        :order_number, :customer_name, :customer_email, :customer_phone, :customer_phone_alt,
        :city_id, :area_id, :shipping_address, :landmark, :delivery_time, :delivery_date,
        :subtotal, :shipping_cost, :tax_amount, :total_amount, :payment_method, :notes,
        'pending', NOW()
    )";
    
    $order_stmt = $conn->prepare($order_query);
    $order_stmt->bindParam(':order_number', $order_number);
    $order_stmt->bindParam(':customer_name', $customer_name);
    $order_stmt->bindParam(':customer_email', $customer_email);
    $order_stmt->bindParam(':customer_phone', $customer_phone);
    $order_stmt->bindParam(':customer_phone_alt', $customer_phone_alt);
    $order_stmt->bindParam(':city_id', $city_id);
    $order_stmt->bindParam(':area_id', $area_id);
    $order_stmt->bindParam(':shipping_address', $address);
    $order_stmt->bindParam(':landmark', $landmark);
    $order_stmt->bindParam(':delivery_time', $delivery_time);
    $order_stmt->bindParam(':delivery_date', $delivery_date);
    $order_stmt->bindParam(':subtotal', $subtotal);
    $order_stmt->bindParam(':shipping_cost', $shipping_cost);
    $order_stmt->bindParam(':tax_amount', $tax_amount);
    $order_stmt->bindParam(':total_amount', $total);
    $order_stmt->bindParam(':payment_method', $payment_method);
    $order_stmt->bindParam(':notes', $notes);
    
    if (!$order_stmt->execute()) {
        throw new Exception('فشل في إنشاء الطلب');
    }
    
    $order_id = $conn->lastInsertId();
    
    // إدراج عناصر الطلب
    $order_item_query = "INSERT INTO order_items (
        order_id, product_id, title, price, quantity, color_id, size_id, total
    ) VALUES (
        :order_id, :product_id, :title, :price, :quantity, :color_id, :size_id, :total
    )";
    
    $order_item_stmt = $conn->prepare($order_item_query);
    
    foreach ($cart_items as $item) {
        $item_price = ($item['sale_price'] && $item['sale_price'] > 0) ? $item['sale_price'] : $item['price'];
        $item_total = $item_price * $item['quantity'];

        $order_item_stmt->bindParam(':order_id', $order_id);
        $order_item_stmt->bindParam(':product_id', $item['product_id']);
        $order_item_stmt->bindParam(':title', $item['title']);
        $order_item_stmt->bindParam(':price', $item_price);
        $order_item_stmt->bindParam(':quantity', $item['quantity']);
        $order_item_stmt->bindParam(':color_id', $item['color_id']);
        $order_item_stmt->bindParam(':size_id', $item['size_id']);
        $order_item_stmt->bindParam(':total', $item_total);

        if (!$order_item_stmt->execute()) {
            throw new Exception('فشل في إضافة عناصر الطلب');
        }
    }
    
    // تأكيد المعاملة
    $conn->commit();
    
    // مسح السلة
    $cart->clear_cart();
    
    // إرسال بريد إلكتروني للعميل (اختياري)
    send_order_confirmation_email($customer_email, $order_number, $total);
    
    // إرسال إشعار للإدارة (اختياري)
    send_admin_notification($order_id, $order_number);
    
    // حفظ معرف الطلب في الجلسة
    $_SESSION['order_id'] = $order_id;
    $_SESSION['order_number'] = $order_number;
    $_SESSION['success_message'] = 'تم إنشاء طلبك بنجاح! رقم الطلب: ' . $order_number;
    
    // التوجه لصفحة تأكيد الطلب
    header('Location: order_success.php?order=' . $order_number);
    exit();
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    if (isset($conn)) {
        $conn->rollback();
    }
    
    $_SESSION['error_message'] = 'حدث خطأ أثناء معالجة طلبك: ' . $e->getMessage();
    header('Location: checkout.php');
    exit();
}

// دوال مساعدة
function send_order_confirmation_email($email, $order_number, $total) {
    // يمكن تطوير هذه الدالة لإرسال بريد إلكتروني فعلي
    // باستخدام PHPMailer أو أي مكتبة أخرى
    return true;
}

function send_admin_notification($order_id, $order_number) {
    // يمكن تطوير هذه الدالة لإرسال إشعار للإدارة
    // عبر البريد الإلكتروني أو SMS أو أي وسيلة أخرى
    return true;
}
?>
