<?php
require_once 'config/database.php';
require_once 'includes/Product.php';
require_once 'includes/Category.php';
require_once 'includes/Cart.php';

// التحقق من وجود slug
if (!isset($_GET['slug']) || empty($_GET['slug'])) {
    header('Location: index.php');
    exit();
}

$slug = sanitize_input($_GET['slug']);

// إنشاء كائنات الكلاسات
$product = new Product();
$category = new Category();
$cart = new Cart();

// جلب القسم
if (!$category->read_by_slug($slug)) {
    header('Location: index.php');
    exit();
}

$page_title = $category->name;

// معاملات البحث والتصفية
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$sort = isset($_GET['sort']) ? sanitize_input($_GET['sort']) : 'newest';
$min_price = isset($_GET['min_price']) ? (float)$_GET['min_price'] : null;
$max_price = isset($_GET['max_price']) ? (float)$_GET['max_price'] : null;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 12;
$offset = ($page - 1) * $limit;

// جلب المنتجات
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // بناء الاستعلام
    $where_conditions = ['p.category_id = :category_id', 'p.is_active = 1'];
    $params = [':category_id' => $category->id];
    
    if (!empty($search)) {
        $where_conditions[] = "(p.title LIKE :search OR p.description LIKE :search)";
        $params[':search'] = "%{$search}%";
    }
    
    if ($min_price !== null) {
        $where_conditions[] = "COALESCE(p.sale_price, p.price) >= :min_price";
        $params[':min_price'] = $min_price;
    }
    
    if ($max_price !== null) {
        $where_conditions[] = "COALESCE(p.sale_price, p.price) <= :max_price";
        $params[':max_price'] = $max_price;
    }
    
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    
    // ترتيب النتائج
    $order_clause = '';
    switch ($sort) {
        case 'price_low':
            $order_clause = 'ORDER BY COALESCE(p.sale_price, p.price) ASC';
            break;
        case 'price_high':
            $order_clause = 'ORDER BY COALESCE(p.sale_price, p.price) DESC';
            break;
        case 'name':
            $order_clause = 'ORDER BY p.title ASC';
            break;
        case 'featured':
            $order_clause = 'ORDER BY p.is_featured DESC, p.created_at DESC';
            break;
        default:
            $order_clause = 'ORDER BY p.created_at DESC';
    }
    
    // استعلام العد
    $count_query = "SELECT COUNT(*) as total FROM products p {$where_clause}";
    $count_stmt = $conn->prepare($count_query);
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    $count_stmt->execute();
    $total_products = $count_stmt->fetch()['total'];
    
    // استعلام البيانات
    $products_query = "SELECT p.* FROM products p {$where_clause} {$order_clause} LIMIT :limit OFFSET :offset";
    $products_stmt = $conn->prepare($products_query);
    foreach ($params as $key => $value) {
        $products_stmt->bindValue($key, $value);
    }
    $products_stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $products_stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $products_stmt->execute();
    $products = $products_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // حساب عدد الصفحات
    $total_pages = ceil($total_products / $limit);
    
    // جلب الأقسام الفرعية
    $subcategories_stmt = $category->get_subcategories($category->id);
    $subcategories = $subcategories_stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error_message = "خطأ في جلب المنتجات";
    $products = [];
    $total_products = 0;
    $total_pages = 0;
    $subcategories = [];
}

// عدد العناصر في السلة
$cart_count = $cart->get_cart_count();

// إعداد عنوان الصفحة ووصف SEO
$page_title = !empty($category->seo_title) ? $category->seo_title : $category->name;
$page_description = !empty($category->description) ? $category->description : '';
$page_keywords = $category->name . ', فساتين, أزياء, موضة';

$breadcrumb = [
    ['name' => 'الرئيسية', 'url' => 'index.php'],
    ['name' => $category->name]
];

include 'includes/header.php';
?>



<!-- Subcategories -->
<?php if (!empty($subcategories)): ?>
<section class="subcategories-section">
    <div class="container">
        <h3>الأقسام الفرعية</h3>
        <div class="subcategories-grid">
            <?php foreach ($subcategories as $subcat): ?>
            <a href="category.php?slug=<?php echo $subcat['slug']; ?>" class="subcategory-card">
                <?php if ($subcat['image']): ?>
                    <img src="<?php echo UPLOAD_URL . $subcat['image']; ?>" alt="<?php echo $subcat['name']; ?>">
                <?php else: ?>
                    <div class="no-image">
                        <i class="fas fa-image"></i>
                    </div>
                <?php endif; ?>
                <h4><?php echo $subcat['name']; ?></h4>
            </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Filters and Products -->
<section class="products-section">
    <div class="container">
        <div class="products-layout">
            
            <!-- Sidebar Filters -->
            <div class="products-sidebar">
                <div class="filters-card">
                    <h3>تصفية النتائج</h3>
                    
                    <form method="GET" class="filters-form" id="filtersForm">
                        <input type="hidden" name="slug" value="<?php echo $slug; ?>">
                        
                        <!-- Search -->
                        <div class="filter-group">
                            <label class="filter-label">البحث</label>
                            <input type="text" name="search" placeholder="ابحث في المنتجات..." 
                                   value="<?php echo htmlspecialchars($search); ?>" class="filter-input">
                        </div>
                        
                        <!-- Price Range -->
                        <div class="filter-group">
                            <label class="filter-label">نطاق السعر</label>
                            <div class="price-range">
                                <input type="number" name="min_price" placeholder="من" 
                                       value="<?php echo $min_price; ?>" class="filter-input">
                                <input type="number" name="max_price" placeholder="إلى" 
                                       value="<?php echo $max_price; ?>" class="filter-input">
                            </div>
                        </div>
                        
                        <div class="filter-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                                تطبيق
                            </button>
                            <a href="category.php?slug=<?php echo $slug; ?>" class="btn btn-outline">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Products Content -->
            <div class="products-content">
                
                <!-- Products Header -->
                <div class="products-header">
                    <div class="products-count">
                        عرض <?php echo $offset + 1; ?> - <?php echo min($offset + $limit, $total_products); ?> 
                        من <?php echo $total_products; ?> منتج
                    </div>
                    
                    <div class="products-sort">
                        <select name="sort" onchange="updateSort(this.value)" class="sort-select">
                            <option value="newest" <?php echo $sort === 'newest' ? 'selected' : ''; ?>>الأحدث</option>
                            <option value="featured" <?php echo $sort === 'featured' ? 'selected' : ''; ?>>المميز</option>
                            <option value="price_low" <?php echo $sort === 'price_low' ? 'selected' : ''; ?>>السعر: من الأقل للأعلى</option>
                            <option value="price_high" <?php echo $sort === 'price_high' ? 'selected' : ''; ?>>السعر: من الأعلى للأقل</option>
                            <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>الاسم</option>
                        </select>
                    </div>
                </div>
                
                <!-- Products Grid -->
                <?php if (!empty($products)): ?>
                <div class="products-grid">
                    <?php foreach ($products as $prod): ?>
                    <div class="product-card">
                        <div class="product-image">
                            <?php if ($prod['featured_image']): ?>
                                <img src="<?php echo UPLOAD_URL . $prod['featured_image']; ?>" alt="<?php echo $prod['title']; ?>">
                            <?php else: ?>
                                <div class="no-image">
                                    <i class="fas fa-image"></i>
                                </div>
                            <?php endif; ?>
                            
                            <div class="product-badges">
                                <?php if ($prod['is_featured']): ?>
                                    <span class="badge featured">مميز</span>
                                <?php endif; ?>
                                
                                <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                                    <span class="badge sale">خصم</span>
                                <?php endif; ?>
                                
                                <?php if ($prod['stock_status'] == 'out_of_stock'): ?>
                                    <span class="badge out-of-stock">نفذ</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="product-actions">
                                <button class="action-btn quick-view" data-product-id="<?php echo $prod['id']; ?>">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn add-to-wishlist" data-product-id="<?php echo $prod['id']; ?>">
                                    <i class="fas fa-heart"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="product-info">
                            <h3 class="product-title">
                                <a href="product.php?slug=<?php echo $prod['slug']; ?>"><?php echo $prod['title']; ?></a>
                            </h3>
                            
                            <div class="product-price">
                                <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                                    <span class="sale-price"><?php echo format_price($prod['sale_price']); ?></span>
                                    <span class="original-price"><?php echo format_price($prod['price']); ?></span>
                                <?php else: ?>
                                    <span class="current-price"><?php echo format_price($prod['price']); ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="product-rating">
                                <div class="stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="rating-count">(<?php echo rand(5, 50); ?>)</span>
                            </div>
                            
                            <?php if ($prod['stock_status'] == 'in_stock'): ?>
                            <button class="btn btn-primary add-to-cart" data-product-id="<?php echo $prod['id']; ?>">
                                <i class="fas fa-shopping-cart"></i>
                                أضيفي للسلة
                            </button>
                            <?php else: ?>
                            <button class="btn btn-disabled" disabled>
                                <i class="fas fa-times"></i>
                                نفذ من المخزن
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <div class="pagination-wrapper">
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?slug=<?php echo $slug; ?>&page=<?php echo $page - 1; ?>&<?php echo http_build_query($_GET); ?>" class="pagination-btn">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <a href="?slug=<?php echo $slug; ?>&page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>" 
                               class="pagination-btn <?php echo $i === $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <a href="?slug=<?php echo $slug; ?>&page=<?php echo $page + 1; ?>&<?php echo http_build_query($_GET); ?>" class="pagination-btn">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h3>لا توجد منتجات</h3>
                    <p>لم يتم العثور على منتجات تطابق معايير البحث</p>
                    <a href="category.php?slug=<?php echo $slug; ?>" class="btn btn-primary">
                        <i class="fas fa-refresh"></i>
                        عرض جميع المنتجات
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- SEO Content Section -->
<?php if (!empty($category->seo_content)): ?>
<section class="seo-content-section">
    <div class="container">
        <div class="seo-content">
            <div class="seo-content-inner">
                <?php echo nl2br(htmlspecialchars($category->seo_content)); ?>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<script>
function updateSort(sortValue) {
    const url = new URL(window.location);
    url.searchParams.set('sort', sortValue);
    url.searchParams.delete('page'); // Reset to first page
    window.location.href = url.toString();
}

// Auto-submit filters on change
document.addEventListener('DOMContentLoaded', function() {
    const filterInputs = document.querySelectorAll('.filter-input');
    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Auto-submit after a delay
            setTimeout(() => {
                document.getElementById('filtersForm').submit();
            }, 500);
        });
    });
});
</script>

<style>
.category-header {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 60px 0;
}

.category-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;
}

.category-info h1 {
    font-size: 36px;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 12px;
}

.category-info p {
    color: #64748b;
    font-size: 16px;
    margin-bottom: 16px;
}

.category-stats {
    color: #6366f1;
    font-weight: 600;
}

.category-image {
    width: 200px;
    height: 200px;
    border-radius: 16px;
    overflow: hidden;
}

.category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.subcategories-section {
    padding: 40px 0;
    background: white;
}

.subcategories-section h3 {
    margin-bottom: 24px;
    font-size: 24px;
    font-weight: 600;
    color: #1a202c;
}

.subcategories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 20px;
}

.subcategory-card {
    text-decoration: none;
    color: inherit;
    text-align: center;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s;
}

.subcategory-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border-color: #6366f1;
}

.subcategory-card img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 12px;
}

.subcategory-card .no-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #f1f5f9;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    color: #9ca3af;
    font-size: 24px;
}

.subcategory-card h4 {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
}

.products-section {
    padding: 40px 0;
}

.products-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 40px;
}

.products-sidebar {
    position: sticky;
    top: 120px;
    height: fit-content;
}

.filters-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filters-card h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #1a202c;
}

.filter-group {
    margin-bottom: 20px;
}

.filter-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 14px;
}

.filter-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
}

.filter-input:focus {
    outline: none;
    border-color: #6366f1;
}

.price-range {
    display: flex;
    gap: 8px;
}

.filter-actions {
    display: flex;
    gap: 8px;
}

.filter-actions .btn {
    flex: 1;
    justify-content: center;
    font-size: 14px;
    padding: 10px 16px;
}

.products-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
}

.products-count {
    color: #64748b;
    font-size: 14px;
}

.sort-select {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 14px;
}

.pagination-wrapper {
    margin-top: 40px;
    display: flex;
    justify-content: center;
}

.pagination {
    display: flex;
    gap: 8px;
}

.pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    text-decoration: none;
    color: #374151;
    transition: all 0.2s;
}

.pagination-btn:hover,
.pagination-btn.active {
    background: #6366f1;
    color: white;
    border-color: #6366f1;
}

@media (max-width: 768px) {
    .category-header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .category-image {
        width: 150px;
        height: 150px;
    }
    
    .products-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .products-sidebar {
        position: static;
    }
    
    .subcategories-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}

/* SEO Content Section */
.seo-content-section {
    background: #f8fafc;
    padding: 60px 0;
    margin-top: 40px;
    border-top: 1px solid #e5e7eb;
}

.seo-content {
    max-width: 800px;
    margin: 0 auto;
}

.seo-content-inner {
    background: white;
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    line-height: 1.8;
    font-size: 16px;
    color: #374151;
}

.seo-content-inner p {
    margin-bottom: 20px;
}

.seo-content-inner h2,
.seo-content-inner h3 {
    color: #1f2937;
    margin-top: 30px;
    margin-bottom: 15px;
    font-weight: 600;
}

.seo-content-inner h2 {
    font-size: 24px;
}

.seo-content-inner h3 {
    font-size: 20px;
}

@media (max-width: 768px) {
    .seo-content-section {
        padding: 40px 0;
    }

    .seo-content-inner {
        padding: 30px 20px;
        margin: 0 15px;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
