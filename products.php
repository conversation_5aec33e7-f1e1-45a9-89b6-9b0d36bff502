<?php
require_once 'config/database.php';
require_once 'includes/Product.php';
require_once 'includes/Category.php';
require_once 'includes/Cart.php';

$page_title = 'جميع المنتجات';

// إنشاء كائنات الكلاسات
$product = new Product();
$category = new Category();
$cart = new Cart();

// معاملات البحث والتصفية
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : null;
$featured = isset($_GET['featured']) ? (int)$_GET['featured'] : null;
$sort = isset($_GET['sort']) ? sanitize_input($_GET['sort']) : 'newest';
$min_price = isset($_GET['min_price']) ? (float)$_GET['min_price'] : null;
$max_price = isset($_GET['max_price']) ? (float)$_GET['max_price'] : null;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 12;
$offset = ($page - 1) * $limit;

// جلب المنتجات
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // بناء الاستعلام
    $where_conditions = ['p.is_active = 1'];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(p.title LIKE :search OR p.description LIKE :search)";
        $params[':search'] = "%{$search}%";
    }
    
    if ($category_filter) {
        $where_conditions[] = "p.category_id = :category_id";
        $params[':category_id'] = $category_filter;
    }
    
    if ($featured) {
        $where_conditions[] = "p.is_featured = 1";
    }
    
    if ($min_price !== null) {
        $where_conditions[] = "COALESCE(p.sale_price, p.price) >= :min_price";
        $params[':min_price'] = $min_price;
    }
    
    if ($max_price !== null) {
        $where_conditions[] = "COALESCE(p.sale_price, p.price) <= :max_price";
        $params[':max_price'] = $max_price;
    }
    
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    
    // ترتيب النتائج
    $order_clause = '';
    switch ($sort) {
        case 'price_low':
            $order_clause = 'ORDER BY COALESCE(p.sale_price, p.price) ASC';
            break;
        case 'price_high':
            $order_clause = 'ORDER BY COALESCE(p.sale_price, p.price) DESC';
            break;
        case 'name':
            $order_clause = 'ORDER BY p.title ASC';
            break;
        case 'featured':
            $order_clause = 'ORDER BY p.is_featured DESC, p.created_at DESC';
            break;
        case 'popular':
            $order_clause = 'ORDER BY p.views_count DESC';
            break;
        default:
            $order_clause = 'ORDER BY p.created_at DESC';
    }
    
    // استعلام العد
    $count_query = "SELECT COUNT(*) as total FROM products p 
                    LEFT JOIN categories c ON p.category_id = c.id 
                    {$where_clause}";
    $count_stmt = $conn->prepare($count_query);
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    $count_stmt->execute();
    $total_products = $count_stmt->fetch()['total'];
    
    // استعلام البيانات
    $products_query = "SELECT p.*, c.name as category_name 
                       FROM products p 
                       LEFT JOIN categories c ON p.category_id = c.id 
                       {$where_clause} 
                       {$order_clause} 
                       LIMIT :limit OFFSET :offset";
    $products_stmt = $conn->prepare($products_query);
    foreach ($params as $key => $value) {
        $products_stmt->bindValue($key, $value);
    }
    $products_stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $products_stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $products_stmt->execute();
    $products = $products_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // حساب عدد الصفحات
    $total_pages = ceil($total_products / $limit);
    
} catch (Exception $e) {
    $error_message = "خطأ في جلب المنتجات";
    $products = [];
    $total_products = 0;
    $total_pages = 0;
}

// جلب الأقسام للفلترة
$categories_stmt = $category->read();
$categories = $categories_stmt->fetchAll(PDO::FETCH_ASSOC);

// عدد العناصر في السلة
$cart_count = $cart->get_cart_count();

$breadcrumb = [
    ['name' => 'جميع المنتجات']
];

include 'includes/header.php';
?>



<!-- Products Section -->
<section class="products-section">
    <div class="container">
        <div class="products-layout">
            
            <!-- Sidebar Filters -->
            <div class="products-sidebar">
                <div class="filters-card">
                    <h3>تصفية النتائج</h3>
                    
                    <form method="GET" class="filters-form" id="filtersForm">
                        
                        <!-- Search -->
                        <div class="filter-group">
                            <label class="filter-label">البحث</label>
                            <input type="text" name="search" placeholder="ابحث في المنتجات..." 
                                   value="<?php echo htmlspecialchars($search); ?>" class="filter-input">
                        </div>
                        
                        <!-- Categories -->
                        <div class="filter-group">
                            <label class="filter-label">الأقسام</label>
                            <select name="category" class="filter-select">
                                <option value="">جميع الأقسام</option>
                                <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo $cat['id']; ?>" 
                                        <?php echo $category_filter == $cat['id'] ? 'selected' : ''; ?>>
                                    <?php echo $cat['name']; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <!-- Featured -->
                        <div class="filter-group">
                            <label class="filter-checkbox">
                                <input type="checkbox" name="featured" value="1" 
                                       <?php echo $featured ? 'checked' : ''; ?>>
                                <span class="checkmark"></span>
                                المنتجات المميزة فقط
                            </label>
                        </div>
                        
                        <!-- Price Range -->
                        <div class="filter-group">
                            <label class="filter-label">نطاق السعر</label>
                            <div class="price-range">
                                <input type="number" name="min_price" placeholder="من" 
                                       value="<?php echo $min_price; ?>" class="filter-input">
                                <input type="number" name="max_price" placeholder="إلى" 
                                       value="<?php echo $max_price; ?>" class="filter-input">
                            </div>
                        </div>
                        
                        <div class="filter-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                                تطبيق
                            </button>
                            <a href="products.php" class="btn btn-outline">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
                
                <!-- Quick Categories -->
                <div class="quick-categories">
                    <h4>الأقسام الشائعة</h4>
                    <div class="quick-categories-list">
                        <?php foreach (array_slice($categories, 0, 6) as $cat): ?>
                        <a href="category.php?slug=<?php echo $cat['slug']; ?>" class="quick-category">
                            <?php echo $cat['name']; ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <!-- Products Content -->
            <div class="products-content">
                
                <!-- Products Header -->
                <div class="products-header">
                    <div class="products-count">
                        عرض <?php echo $offset + 1; ?> - <?php echo min($offset + $limit, $total_products); ?> 
                        من <?php echo $total_products; ?> منتج
                    </div>
                    
                    <div class="products-controls">
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="grid">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="view-btn" data-view="list">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                        
                        <select name="sort" onchange="updateSort(this.value)" class="sort-select">
                            <option value="newest" <?php echo $sort === 'newest' ? 'selected' : ''; ?>>الأحدث</option>
                            <option value="featured" <?php echo $sort === 'featured' ? 'selected' : ''; ?>>المميز</option>
                            <option value="popular" <?php echo $sort === 'popular' ? 'selected' : ''; ?>>الأكثر مشاهدة</option>
                            <option value="price_low" <?php echo $sort === 'price_low' ? 'selected' : ''; ?>>السعر: من الأقل للأعلى</option>
                            <option value="price_high" <?php echo $sort === 'price_high' ? 'selected' : ''; ?>>السعر: من الأعلى للأقل</option>
                            <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>الاسم</option>
                        </select>
                    </div>
                </div>
                
                <!-- Products Grid -->
                <?php if (!empty($products)): ?>
                <div class="products-grid" id="productsGrid">
                    <?php foreach ($products as $prod): ?>
                    <div class="product-card">
                        <div class="product-image">
                            <?php if ($prod['featured_image']): ?>
                                <img src="<?php echo UPLOAD_URL . $prod['featured_image']; ?>" alt="<?php echo $prod['title']; ?>">
                            <?php else: ?>
                                <div class="no-image">
                                    <i class="fas fa-image"></i>
                                </div>
                            <?php endif; ?>
                            
                            <div class="product-badges">
                                <?php if ($prod['is_featured']): ?>
                                    <span class="badge featured">مميز</span>
                                <?php endif; ?>
                                
                                <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                                    <span class="badge sale">خصم</span>
                                <?php endif; ?>
                                
                                <?php if ($prod['stock_status'] == 'out_of_stock'): ?>
                                    <span class="badge out-of-stock">نفذ</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="product-actions">
                                <button class="action-btn quick-view" data-product-id="<?php echo $prod['id']; ?>">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn add-to-wishlist" data-product-id="<?php echo $prod['id']; ?>">
                                    <i class="fas fa-heart"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="product-info">
                            <div class="product-category">
                                <a href="category.php?slug=<?php echo $prod['category_name']; ?>"><?php echo $prod['category_name']; ?></a>
                            </div>
                            
                            <h3 class="product-title">
                                <a href="product.php?slug=<?php echo $prod['slug']; ?>"><?php echo $prod['title']; ?></a>
                            </h3>
                            
                            <div class="product-price">
                                <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                                    <span class="sale-price"><?php echo format_price($prod['sale_price']); ?></span>
                                    <span class="original-price"><?php echo format_price($prod['price']); ?></span>
                                    <span class="discount-percentage">
                                        <?php 
                                        $discount = (($prod['price'] - $prod['sale_price']) / $prod['price']) * 100;
                                        echo round($discount) . '% خصم';
                                        ?>
                                    </span>
                                <?php else: ?>
                                    <span class="current-price"><?php echo format_price($prod['price']); ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="product-rating">
                                <div class="stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="rating-count">(<?php echo rand(5, 50); ?>)</span>
                            </div>
                            
                            <div class="product-views">
                                <i class="fas fa-eye"></i>
                                <span><?php echo number_format($prod['views_count']); ?> مشاهدة</span>
                            </div>
                            
                            <?php if ($prod['stock_status'] == 'in_stock'): ?>
                            <button class="btn btn-primary add-to-cart" data-product-id="<?php echo $prod['id']; ?>">
                                <i class="fas fa-shopping-cart"></i>
                                أضيفي للسلة
                            </button>
                            <?php else: ?>
                            <button class="btn btn-disabled" disabled>
                                <i class="fas fa-times"></i>
                                نفذ من المخزن
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <div class="pagination-wrapper">
                    <div class="pagination-info">
                        عرض <?php echo $offset + 1; ?> إلى <?php echo min($offset + $limit, $total_products); ?> 
                        من <?php echo $total_products; ?> منتج
                    </div>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)); ?>" class="pagination-btn">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <a href="?page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)); ?>" 
                               class="pagination-btn <?php echo $i === $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <a href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)); ?>" class="pagination-btn">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h3>لا توجد منتجات</h3>
                    <p>لم يتم العثور على منتجات تطابق معايير البحث</p>
                    <a href="products.php" class="btn btn-primary">
                        <i class="fas fa-refresh"></i>
                        عرض جميع المنتجات
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<script>
function updateSort(sortValue) {
    const url = new URL(window.location);
    url.searchParams.set('sort', sortValue);
    url.searchParams.delete('page'); // Reset to first page
    window.location.href = url.toString();
}

// View toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const viewButtons = document.querySelectorAll('.view-btn');
    const productsGrid = document.getElementById('productsGrid');
    
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const view = this.dataset.view;
            
            // Update active button
            viewButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Update grid class
            if (view === 'list') {
                productsGrid.classList.add('list-view');
            } else {
                productsGrid.classList.remove('list-view');
            }
        });
    });
    
    // Auto-submit filters on change
    const filterInputs = document.querySelectorAll('.filter-input, .filter-select');
    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            setTimeout(() => {
                document.getElementById('filtersForm').submit();
            }, 300);
        });
    });
});
</script>

<style>


.products-section {
    padding: 40px 0;
}

.products-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 40px;
}

.products-sidebar {
    position: sticky;
    top: 120px;
    height: fit-content;
}

.filters-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin-bottom: 24px;
}

.filters-card h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #1a202c;
}

.filter-group {
    margin-bottom: 20px;
}

.filter-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 14px;
}

.filter-input,
.filter-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: #6366f1;
}

.filter-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
}

.filter-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    transition: all 0.2s;
}

.filter-checkbox input[type="checkbox"]:checked + .checkmark {
    background: #6366f1;
    border-color: #6366f1;
}

.filter-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.price-range {
    display: flex;
    gap: 8px;
}

.filter-actions {
    display: flex;
    gap: 8px;
}

.filter-actions .btn {
    flex: 1;
    justify-content: center;
    font-size: 14px;
    padding: 10px 16px;
}

.quick-categories {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.quick-categories h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #1a202c;
}

.quick-categories-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.quick-category {
    padding: 8px 12px;
    background: #f8fafc;
    border-radius: 6px;
    text-decoration: none;
    color: #374151;
    font-size: 14px;
    transition: all 0.2s;
}

.quick-category:hover {
    background: #6366f1;
    color: white;
}

.products-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
}

.products-count {
    color: #64748b;
    font-size: 14px;
}

.products-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.view-toggle {
    display: flex;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    overflow: hidden;
}

.view-btn {
    padding: 8px 12px;
    background: white;
    border: none;
    cursor: pointer;
    color: #64748b;
    transition: all 0.2s;
}

.view-btn.active,
.view-btn:hover {
    background: #6366f1;
    color: white;
}

.sort-select {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 14px;
}

.product-category {
    margin-bottom: 8px;
}

.product-category a {
    color: #6366f1;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-views {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #9ca3af;
    font-size: 12px;
    margin-bottom: 12px;
}

.discount-percentage {
    background: #ef4444;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
    margin-right: 8px;
}

.products-grid.list-view {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.products-grid.list-view .product-card {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
}

.products-grid.list-view .product-image {
    width: 150px;
    height: 150px;
    flex-shrink: 0;
}

.products-grid.list-view .product-info {
    flex: 1;
}

@media (max-width: 768px) {
    .page-header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .page-image {
        width: 150px;
        height: 150px;
    }
    
    .products-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .products-sidebar {
        position: static;
    }
    
    .products-controls {
        flex-direction: column;
        gap: 12px;
    }
    
    .products-grid.list-view .product-card {
        flex-direction: column;
        text-align: center;
    }
    
    .products-grid.list-view .product-image {
        width: 100%;
        height: 200px;
    }
}
</style>

<script>
// Products page functionality
document.addEventListener('DOMContentLoaded', function() {
    initProductsPage();
});

function initProductsPage() {
    // Add to cart functionality for product cards
    const addToCartButtons = document.querySelectorAll('.add-to-cart');

    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const productId = this.dataset.productId;
            if (!productId) {
                showToast('معرف المنتج غير صحيح', 'error');
                return;
            }

            // Create form data
            const formData = new FormData();
            formData.append('product_id', productId);
            formData.append('quantity', '1');

            // Add to cart
            addToCartFromCard(formData, this);
        });
    });
}

function addToCartFromCard(formData, button) {
    const originalText = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإضافة...';
    button.disabled = true;

    fetch('ajax/add-to-cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('تم إضافة المنتج للسلة بنجاح', 'success');
            updateCartCount(data.cart_count);

            // Show success animation
            button.innerHTML = '<i class="fas fa-check"></i> تم الإضافة';
            button.style.background = '#10b981';

            setTimeout(() => {
                button.innerHTML = originalText;
                button.style.background = '';
                button.disabled = false;
            }, 2000);
        } else {
            showToast(data.message || 'حدث خطأ أثناء إضافة المنتج', 'error');
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ في الاتصال', 'error');
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Show toast notification
function showToast(message, type = 'info') {
    // Remove existing toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add toast styles
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 12px;
        max-width: 400px;
        animation: slideInRight 0.3s ease;
    `;

    // Add animation styles if not exists
    if (!document.querySelector('#toast-styles')) {
        const styles = document.createElement('style');
        styles.id = 'toast-styles';
        styles.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            .toast-content {
                display: flex;
                align-items: center;
                gap: 8px;
                flex: 1;
            }
            .toast-close {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                opacity: 0.8;
            }
            .toast-close:hover {
                opacity: 1;
                background: rgba(255, 255, 255, 0.1);
            }
        `;
        document.head.appendChild(styles);
    }

    // Add to page
    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}

// Update cart count in header
function updateCartCount(count) {
    const cartCountElements = document.querySelectorAll('.cart-count');
    cartCountElements.forEach(element => {
        element.textContent = count;
        if (count > 0) {
            element.style.display = 'inline-block';
        }
    });
}
</script>

<?php include 'includes/footer.php'; ?>
