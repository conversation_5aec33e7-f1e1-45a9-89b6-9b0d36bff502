<?php
require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/SlugHelper.php';

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$success_message = '';
$error_message = '';

// جلب التصنيفات والألوان والمقاسات
try {
    $categories_stmt = $conn->query("SELECT id, name FROM categories WHERE is_active = 1 ORDER BY name");
    $categories = $categories_stmt->fetchAll();
    
    $colors_stmt = $conn->query("SELECT id, name, hex_code FROM colors WHERE is_active = 1 ORDER BY name");
    $colors = $colors_stmt->fetchAll();
    
    $sizes_stmt = $conn->query("SELECT id, name FROM sizes WHERE is_active = 1 ORDER BY sort_order, name");
    $sizes = $sizes_stmt->fetchAll();
} catch (Exception $e) {
    $error_message = "حدث خطأ في جلب البيانات";
}

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = trim($_POST['title'] ?? '');
    $slug = trim($_POST['slug'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $sale_price = !empty($_POST['sale_price']) ? floatval($_POST['sale_price']) : null;
    $category_id = intval($_POST['category_id'] ?? 0);
    $stock_status = $_POST['stock_status'] ?? 'in_stock';
    $video_url = trim($_POST['video_url'] ?? '');
    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
    $selected_colors = $_POST['colors'] ?? [];
    $selected_sizes = $_POST['sizes'] ?? [];

    // إنشاء رابط تلقائي إذا كان فارغاً
    if (empty($slug) && !empty($title)) {
        $slug = SlugHelper::createProductSlug($title);
    }

    // التحقق من صحة البيانات
    if (empty($title)) {
        $error_message = 'عنوان المنتج مطلوب';
    } elseif (empty($slug)) {
        $error_message = 'رابط المنتج مطلوب';
    } elseif ($price <= 0) {
        $error_message = 'سعر المنتج يجب أن يكون أكبر من صفر';
    } elseif ($category_id <= 0) {
        $error_message = 'يجب اختيار تصنيف للمنتج';
    } else {
        // التأكد من فرادة الرابط
        $slug = SlugHelper::ensureUniqueSlug($slug, 'products', 'slug');

        if (true) { // إزالة فحص التكرار لأن ensureUniqueSlug يتولى ذلك
            try {
                $conn->beginTransaction();
                
                // إدراج المنتج
                $product_query = "INSERT INTO products (title, slug, description, price, sale_price, category_id, stock_status, video_url, is_featured, is_active) 
                                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)";
                $product_stmt = $conn->prepare($product_query);
                $product_stmt->execute([$title, $slug, $description, $price, $sale_price, $category_id, $stock_status, $video_url, $is_featured]);
                
                $product_id = $conn->lastInsertId();
                
                // إضافة الألوان
                if (!empty($selected_colors)) {
                    $color_query = "INSERT INTO product_colors (product_id, color_id) VALUES (?, ?)";
                    $color_stmt = $conn->prepare($color_query);
                    foreach ($selected_colors as $color_id) {
                        $color_stmt->execute([$product_id, $color_id]);
                    }
                }
                
                // إضافة المقاسات
                if (!empty($selected_sizes)) {
                    $size_query = "INSERT INTO product_sizes (product_id, size_id) VALUES (?, ?)";
                    $size_stmt = $conn->prepare($size_query);
                    foreach ($selected_sizes as $size_id) {
                        $size_stmt->execute([$product_id, $size_id]);
                    }
                }
                
                // معالجة رفع الصور
                $uploaded_images = [];
                if (isset($_FILES['gallery_images']) && !empty($_FILES['gallery_images']['name'][0])) {
                    $uploaded_images = array_merge($uploaded_images, uploadProductImages($_FILES['gallery_images'], $product_id, 'gallery'));
                }

                if (isset($_FILES['lifestyle_images']) && !empty($_FILES['lifestyle_images']['name'][0])) {
                    $uploaded_images = array_merge($uploaded_images, uploadProductImages($_FILES['lifestyle_images'], $product_id, 'lifestyle'));
                }

                // معالجة رفع الفيديو
                $video_file_path = null;
                if (isset($_FILES['video_file']) && $_FILES['video_file']['error'] === UPLOAD_ERR_OK) {
                    $video_file_path = uploadProductVideo($_FILES['video_file'], $product_id);
                    if ($video_file_path) {
                        try {
                            // التحقق من وجود عمود video_file
                            $check_column = $conn->query("SHOW COLUMNS FROM products LIKE 'video_file'");
                            if ($check_column->rowCount() > 0) {
                                $update_video = $conn->prepare("UPDATE products SET video_file = ? WHERE id = ?");
                                $update_video->execute([$video_file_path, $product_id]);
                            } else {
                                // إضافة العمود إذا لم يكن موجوداً
                                $conn->exec("ALTER TABLE products ADD COLUMN video_file VARCHAR(255) DEFAULT NULL AFTER video_url");
                                $update_video = $conn->prepare("UPDATE products SET video_file = ? WHERE id = ?");
                                $update_video->execute([$video_file_path, $product_id]);
                            }
                        } catch (Exception $e) {
                            // في حالة الخطأ، نحفظ مسار الفيديو في ملاحظة
                            error_log("خطأ في حفظ ملف الفيديو: " . $e->getMessage());
                        }
                    }
                }

                // تعيين أول صورة كصورة مميزة إذا لم تكن موجودة
                if (!empty($uploaded_images)) {
                    $first_gallery_image = null;
                    foreach ($uploaded_images as $img) {
                        if ($img['type'] === 'gallery') {
                            $first_gallery_image = $img['path'];
                            break;
                        }
                    }

                    if ($first_gallery_image) {
                        $update_featured = $conn->prepare("UPDATE products SET featured_image = ? WHERE id = ?");
                        $update_featured->execute([$first_gallery_image, $product_id]);
                    }
                }

                $conn->commit();
                $success_message = 'تم إضافة المنتج بنجاح' . (!empty($uploaded_images) ? ' مع ' . count($uploaded_images) . ' صورة' : '');

                // إعادة تعيين النموذج
                $title = $slug = $description = $video_url = '';
                $price = $sale_price = 0;
                $category_id = 0;
                $stock_status = 'in_stock';
                $is_featured = 0;
                $selected_colors = $selected_sizes = [];

            } catch (Exception $e) {
                $conn->rollBack();
                $error_message = 'حدث خطأ أثناء إضافة المنتج: ' . $e->getMessage();
            }
        }
    }
}

// دالة رفع صور المنتج
function uploadProductImages($files, $product_id, $image_type) {
    global $conn;

    $upload_dir = '../uploads/products/';
    $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    $max_file_size = 5 * 1024 * 1024; // 5MB
    $uploaded_images = [];

    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    for ($i = 0; $i < count($files['name']); $i++) {
        if ($files['error'][$i] === UPLOAD_ERR_OK) {
            $file_name = $files['name'][$i];
            $file_tmp = $files['tmp_name'][$i];
            $file_size = $files['size'][$i];

            // التحقق من نوع الملف
            $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
            if (!in_array($file_ext, $allowed_types)) {
                continue;
            }

            // التحقق من حجم الملف
            if ($file_size > $max_file_size) {
                continue;
            }

            // إنشاء اسم ملف فريد
            $new_file_name = 'product_' . $product_id . '_' . time() . '_' . $i . '.' . $file_ext;
            $file_path = $upload_dir . $new_file_name;

            // رفع الملف
            if (move_uploaded_file($file_tmp, $file_path)) {
                // حفظ معلومات الصورة في قاعدة البيانات
                try {
                    // الحصول على أعلى ترتيب
                    $sort_stmt = $conn->prepare("SELECT COALESCE(MAX(sort_order), 0) + 1 as next_order FROM product_images WHERE product_id = ? AND image_type = ?");
                    $sort_stmt->execute([$product_id, $image_type]);
                    $sort_order = $sort_stmt->fetch()['next_order'];

                    $stmt = $conn->prepare("INSERT INTO product_images (product_id, image_path, image_type, sort_order, alt_text) VALUES (?, ?, ?, ?, ?)");
                    $alt_text = 'صورة المنتج ' . ($i + 1);
                    $stmt->execute([$product_id, 'products/' . $new_file_name, $image_type, $sort_order, $alt_text]);

                    $uploaded_images[] = [
                        'id' => $conn->lastInsertId(),
                        'path' => 'products/' . $new_file_name,
                        'type' => $image_type,
                        'sort_order' => $sort_order
                    ];

                } catch (Exception $e) {
                    // حذف الملف إذا فشل حفظه في قاعدة البيانات
                    unlink($file_path);
                }
            }
        }
    }

    return $uploaded_images;
}

// دالة رفع فيديو المنتج
function uploadProductVideo($file, $product_id) {
    $upload_dir = '../uploads/videos/';
    $allowed_types = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];
    $max_file_size = 50 * 1024 * 1024; // 50MB

    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    if ($file['error'] === UPLOAD_ERR_OK) {
        $file_name = $file['name'];
        $file_tmp = $file['tmp_name'];
        $file_size = $file['size'];

        // التحقق من نوع الملف
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        if (!in_array($file_ext, $allowed_types)) {
            return false;
        }

        // التحقق من حجم الملف
        if ($file_size > $max_file_size) {
            return false;
        }

        // إنشاء اسم ملف فريد
        $new_file_name = 'video_' . $product_id . '_' . time() . '.' . $file_ext;
        $file_path = $upload_dir . $new_file_name;

        // رفع الملف
        if (move_uploaded_file($file_tmp, $file_path)) {
            return 'videos/' . $new_file_name;
        }
    }

    return false;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة منتج جديد - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            z-index: 1000;
        }
        
        .sidebar .logo {
            padding: 2rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .logo h3 {
            color: white;
            margin: 0;
            font-weight: 600;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .sidebar-nav .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .sidebar-nav .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .sidebar-nav .nav-link:hover,
        .sidebar-nav .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .sidebar-nav .nav-link i {
            width: 20px;
            margin-left: 0.75rem;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header h2 {
            margin: 0;
            color: #333;
            font-weight: 600;
        }
        
        .form-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-section {
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #eee;
        }
        
        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .form-section h5 {
            color: #333;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .color-option {
            display: inline-block;
            margin: 0.25rem;
        }
        
        .color-checkbox {
            display: none;
        }
        
        .color-label {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .color-checkbox:checked + .color-label {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        
        .color-preview {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 0.5rem;
            border: 1px solid #ddd;
        }
        
        .size-option {
            display: inline-block;
            margin: 0.25rem;
        }
        
        .size-checkbox {
            display: none;
        }
        
        .size-label {
            display: block;
            padding: 0.5rem 1rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            min-width: 60px;
        }
        
        .size-checkbox:checked + .size-label {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .btn-secondary {
            border-radius: 10px;
            padding: 0.75rem 2rem;
        }

        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .images-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 0.5rem;
        }

        .preview-item {
            position: relative;
            border-radius: 5px;
            overflow: hidden;
        }

        .preview-item img {
            width: 100%;
            height: 80px;
            object-fit: cover;
        }

        .preview-remove {
            position: absolute;
            top: 2px;
            right: 2px;
            background: rgba(220, 53, 69, 0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            cursor: pointer;
        }

        .video-upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .video-upload-area:hover {
            border-color: #dc2626;
            background: rgba(220, 38, 38, 0.05);
        }

        .video-preview {
            display: none;
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }

        .video-preview.show {
            display: block;
        }

        .video-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .video-info i {
            color: #dc2626;
            font-size: 1.2rem;
        }

        .video-details {
            flex: 1;
        }

        .video-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }

        .video-size {
            font-size: 0.8rem;
            color: #666;
        }

        .video-remove {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 0.7rem;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-crown me-2"></i>لوحة التحكم</h3>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
            <div class="nav-item">
                <a href="products.php" class="nav-link">
                    <i class="fas fa-tshirt"></i>
                    إدارة المنتجات
                </a>
            </div>
            <div class="nav-item">
                <a href="add-product.php" class="nav-link active">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="colors.php" class="nav-link">
                    <i class="fas fa-palette"></i>
                    إدارة الألوان
                </a>
            </div>
            <div class="nav-item">
                <a href="sizes.php" class="nav-link">
                    <i class="fas fa-ruler"></i>
                    إدارة المقاسات
                </a>
            </div>
            <div class="nav-item">
                <a href="logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-plus me-2"></i>إضافة منتج جديد</h2>
                <a href="products.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للمنتجات
                </a>
            </div>
        </div>

        <!-- Alerts -->
        <?php if (!empty($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Product Form -->
        <div class="form-card">
            <form method="POST" action="" enctype="multipart/form-data">
                <!-- Basic Information -->
                <div class="form-section">
                    <h5><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h5>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="title" class="form-label">عنوان المنتج *</label>
                            <input type="text" class="form-control" id="title" name="title"
                                   value="<?php echo htmlspecialchars($title ?? ''); ?>"
                                   required onkeyup="generateSlugFromTitle()">
                        </div>
                        <div class="col-md-6">
                            <label for="slug" class="form-label">رابط المنتج</label>
                            <input type="text" class="form-control" id="slug" name="slug"
                                   value="<?php echo htmlspecialchars($slug ?? ''); ?>"
                                   placeholder="سيتم إنشاؤه تلقائياً من العنوان">
                            <div class="form-text">اتركه فارغاً لإنشاء رابط تلقائي من العنوان</div>
                        </div>
                        <div class="col-md-6">
                            <label for="category_id" class="form-label">التصنيف *</label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">اختر التصنيف</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>"
                                            <?php echo (isset($category_id) && $category_id == $category['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="stock_status" class="form-label">حالة التوفر</label>
                            <select class="form-select" id="stock_status" name="stock_status">
                                <option value="in_stock" <?php echo (isset($stock_status) && $stock_status == 'in_stock') ? 'selected' : ''; ?>>متوفر</option>
                                <option value="out_of_stock" <?php echo (isset($stock_status) && $stock_status == 'out_of_stock') ? 'selected' : ''; ?>>نفذ</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="description" class="form-label">وصف المنتج</label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="اكتب وصفاً مفصلاً للمنتج..."><?php echo htmlspecialchars($description ?? ''); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Pricing -->
                <div class="form-section">
                    <h5><i class="fas fa-tag me-2"></i>الأسعار</h5>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="price" class="form-label">السعر الأساسي (ر.س) *</label>
                            <input type="number" class="form-control" id="price" name="price"
                                   value="<?php echo $price ?? ''; ?>"
                                   step="0.01" min="0" required>
                        </div>
                        <div class="col-md-6">
                            <label for="sale_price" class="form-label">سعر التخفيض (ر.س)</label>
                            <input type="number" class="form-control" id="sale_price" name="sale_price"
                                   value="<?php echo $sale_price ?? ''; ?>"
                                   step="0.01" min="0">
                            <div class="form-text">اتركه فارغاً إذا لم يكن هناك تخفيض</div>
                        </div>
                    </div>
                </div>

                <!-- Colors and Sizes -->
                <div class="form-section">
                    <h5><i class="fas fa-palette me-2"></i>الألوان والمقاسات</h5>
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label class="form-label">الألوان المتوفرة</label>
                            <div class="colors-grid">
                                <?php foreach ($colors as $color): ?>
                                    <div class="color-option">
                                        <input type="checkbox" class="color-checkbox"
                                               id="color_<?php echo $color['id']; ?>"
                                               name="colors[]"
                                               value="<?php echo $color['id']; ?>"
                                               <?php echo in_array($color['id'], $selected_colors ?? []) ? 'checked' : ''; ?>>
                                        <label for="color_<?php echo $color['id']; ?>" class="color-label">
                                            <?php echo htmlspecialchars($color['name']); ?>
                                            <?php if ($color['hex_code']): ?>
                                                <div class="color-preview" style="background-color: <?php echo $color['hex_code']; ?>"></div>
                                            <?php endif; ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">المقاسات المتوفرة</label>
                            <div class="sizes-grid">
                                <?php foreach ($sizes as $size): ?>
                                    <div class="size-option">
                                        <input type="checkbox" class="size-checkbox"
                                               id="size_<?php echo $size['id']; ?>"
                                               name="sizes[]"
                                               value="<?php echo $size['id']; ?>"
                                               <?php echo in_array($size['id'], $selected_sizes ?? []) ? 'checked' : ''; ?>>
                                        <label for="size_<?php echo $size['id']; ?>" class="size-label">
                                            <?php echo htmlspecialchars($size['name']); ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Images Upload -->
                <div class="form-section">
                    <h5><i class="fas fa-images me-2"></i>صور المنتج</h5>
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label class="form-label">صور المعرض</label>
                            <div class="upload-area" onclick="document.getElementById('galleryImages').click()">
                                <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                <p class="mb-1">انقر لاختيار صور المعرض</p>
                                <small class="text-muted">يمكنك اختيار عدة صور (JPG, PNG, GIF, WEBP)</small>
                            </div>
                            <input type="file" id="galleryImages" name="gallery_images[]" multiple accept="image/*" style="display: none;">
                            <div id="galleryPreview" class="images-preview mt-2"></div>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">صور على الطبيعة</label>
                            <div class="upload-area" onclick="document.getElementById('lifestyleImages').click()">
                                <i class="fas fa-camera fa-2x text-muted mb-2"></i>
                                <p class="mb-1">انقر لاختيار صور الطبيعة</p>
                                <small class="text-muted">صور المنتج في بيئة طبيعية أو على موديل</small>
                            </div>
                            <input type="file" id="lifestyleImages" name="lifestyle_images[]" multiple accept="image/*" style="display: none;">
                            <div id="lifestylePreview" class="images-preview mt-2"></div>
                        </div>
                    </div>
                </div>

                <!-- Video Upload -->
                <div class="form-section">
                    <h5><i class="fas fa-video me-2"></i>فيديو المنتج</h5>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="video_url" class="form-label">رابط الفيديو</label>
                            <input type="url" class="form-control" id="video_url" name="video_url"
                                   value="<?php echo htmlspecialchars($video_url ?? ''); ?>"
                                   placeholder="https://www.youtube.com/watch?v=...">
                            <div class="form-text">رابط فيديو من YouTube أو Vimeo</div>
                        </div>
                        <div class="col-md-6">
                            <label for="video_file" class="form-label">أو رفع ملف فيديو</label>
                            <div class="video-upload-area" onclick="document.getElementById('videoFile').click()">
                                <i class="fas fa-video fa-2x text-muted mb-2"></i>
                                <p class="mb-1">انقر لاختيار ملف فيديو</p>
                                <small class="text-muted">MP4, AVI, MOV, WMV (حد أقصى 50MB)</small>
                            </div>
                            <input type="file" id="videoFile" name="video_file" accept="video/*" style="display: none;">
                            <div id="videoPreview" class="video-preview mt-2"></div>
                        </div>
                    </div>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> يمكنك إما إدخال رابط فيديو أو رفع ملف فيديو. إذا تم رفع ملف فيديو، سيتم استخدامه بدلاً من الرابط.
                    </div>
                </div>

                <!-- Settings -->
                <div class="form-section">
                    <h5><i class="fas fa-cog me-2"></i>الإعدادات</h5>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured"
                                       <?php echo (isset($is_featured) && $is_featured) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_featured">
                                    منتج مميز
                                </label>
                                <div class="form-text">سيظهر في قسم المنتجات المميزة</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex gap-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ المنتج
                    </button>
                    <a href="products.php" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function generateSlugFromTitle() {
            const title = document.getElementById('title').value;
            // إرسال طلب AJAX لإنشاء slug من الخادم
            if (title.trim()) {
                fetch('generate_slug.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'title=' + encodeURIComponent(title)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('slug').value = data.slug;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
        }

        // معاينة الصور
        document.getElementById('galleryImages').addEventListener('change', function(e) {
            previewImages(e.target.files, 'galleryPreview');
        });

        document.getElementById('lifestyleImages').addEventListener('change', function(e) {
            previewImages(e.target.files, 'lifestylePreview');
        });

        // معاينة الفيديو
        document.getElementById('videoFile').addEventListener('change', function(e) {
            previewVideo(e.target.files[0]);
        });

        function previewImages(files, containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const div = document.createElement('div');
                        div.className = 'preview-item';
                        div.innerHTML = `
                            <img src="${e.target.result}" alt="معاينة">
                            <button type="button" class="preview-remove" onclick="removePreview(this, '${containerId}')">×</button>
                        `;
                        container.appendChild(div);
                    };
                    reader.readAsDataURL(file);
                }
            }
        }

        function removePreview(button, containerId) {
            button.parentElement.remove();
            // إعادة تعيين input file إذا لم تعد هناك صور
            const container = document.getElementById(containerId);
            if (container.children.length === 0) {
                const inputId = containerId === 'galleryPreview' ? 'galleryImages' : 'lifestyleImages';
                document.getElementById(inputId).value = '';
            }
        }

        // معاينة الفيديو
        function previewVideo(file) {
            const preview = document.getElementById('videoPreview');

            if (file && file.type.startsWith('video/')) {
                const fileSize = (file.size / (1024 * 1024)).toFixed(2); // MB

                preview.innerHTML = `
                    <div class="video-info">
                        <i class="fas fa-video"></i>
                        <div class="video-details">
                            <div class="video-name">${file.name}</div>
                            <div class="video-size">${fileSize} MB</div>
                        </div>
                        <button type="button" class="video-remove" onclick="removeVideo()">×</button>
                    </div>
                `;
                preview.classList.add('show');
            } else {
                preview.classList.remove('show');
                preview.innerHTML = '';
            }
        }

        function removeVideo() {
            document.getElementById('videoFile').value = '';
            document.getElementById('videoPreview').classList.remove('show');
            document.getElementById('videoPreview').innerHTML = '';
        }

        // التحقق من صحة النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            const price = parseFloat(document.getElementById('price').value);
            const salePrice = parseFloat(document.getElementById('sale_price').value);

            if (salePrice && salePrice >= price) {
                e.preventDefault();
                alert('سعر التخفيض يجب أن يكون أقل من السعر الأساسي');
                return false;
            }
        });
    </script>
</body>
</html>
