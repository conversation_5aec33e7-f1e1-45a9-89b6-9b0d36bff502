<?php
// تضمين إعدادات الموقع
require_once __DIR__ . '/settings.php';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo getSiteName(); ?></title>

    <!-- Meta Tags -->
    <meta name="description" content="<?php echo isset($page_description) && !empty($page_description) ? htmlspecialchars($page_description) : htmlspecialchars(getSiteDescription()); ?>">
    <meta name="keywords" content="<?php echo isset($page_keywords) && !empty($page_keywords) ? htmlspecialchars($page_keywords) : htmlspecialchars(getSiteKeywords()); ?>">
    <meta name="author" content="<?php echo htmlspecialchars(getSiteName()); ?>">

    <!-- Open Graph Tags -->
    <meta property="og:title" content="<?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - ' : ''; ?><?php echo htmlspecialchars(getSiteName()); ?>">
    <meta property="og:description" content="<?php echo isset($page_description) && !empty($page_description) ? htmlspecialchars($page_description) : htmlspecialchars(getSiteDescription()); ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/images/og-image.jpg">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:type" content="website">
    
    <!-- CSS Files -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="<?php echo SITE_URL; ?>/css/style.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/images/favicon.ico">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Store",
        "name": "متجر فساتين السهرة",
        "description": "أجمل وأحدث تصاميم فساتين السهرة الأنيقة والراقية",
        "url": "<?php echo SITE_URL; ?>",
        "logo": "<?php echo SITE_URL; ?>/images/logo.png",
        "address": {
            "@type": "PostalAddress",
            "addressCountry": "SA"
        }
    }
    </script>
</head>
<body>
    
    <!-- Top Bar -->
    <div class="top-bar">
        <div class="container">
            <div class="top-bar-content">
                <div class="top-bar-left">
                    <div class="contact-info">
                        <?php if (!empty($contact_phone)): ?>
                        <span><i class="fas fa-phone"></i> <?php echo htmlspecialchars($contact_phone); ?></span>
                        <?php endif; ?>
                        <?php if (!empty($contact_email)): ?>
                        <span><i class="fas fa-envelope"></i> <?php echo htmlspecialchars($contact_email); ?></span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="top-bar-right">
                    <div class="social-links">
                        <?php if (!empty($instagram_url) && $instagram_url != '#'): ?>
                        <a href="<?php echo htmlspecialchars($instagram_url); ?>" class="social-link" target="_blank"><i class="fab fa-instagram"></i></a>
                        <?php endif; ?>
                        <?php if (!empty($twitter_url) && $twitter_url != '#'): ?>
                        <a href="<?php echo htmlspecialchars($twitter_url); ?>" class="social-link" target="_blank"><i class="fab fa-twitter"></i></a>
                        <?php endif; ?>
                        <?php if (!empty($facebook_url) && $facebook_url != '#'): ?>
                        <a href="<?php echo htmlspecialchars($facebook_url); ?>" class="social-link" target="_blank"><i class="fab fa-facebook"></i></a>
                        <?php endif; ?>
                        <?php if (!empty($contact_phone)): ?>
                        <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $contact_phone); ?>" class="social-link" target="_blank"><i class="fab fa-whatsapp"></i></a>
                        <?php endif; ?>
                    </div>
                    <div class="language-selector">
                        <select>
                            <option value="ar">العربية</option>
                            <option value="en">English</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Header -->
    <header class="main-header">
        <div class="container">
            <div class="header-content">
                
                <!-- Logo -->
                <div class="header-logo">
                    <a href="<?php echo SITE_URL; ?>">
                        <?php
                        $logo_path = getLogoPath();
                        if (!empty($logo_path)):
                        ?>
                            <img src="<?php echo $logo_path; ?>" alt="<?php echo htmlspecialchars(getSiteName()); ?>" class="logo-img">
                        <?php else: ?>
                            <h2 class="logo-text"><?php echo htmlspecialchars(getSiteName()); ?></h2>
                        <?php endif; ?>
                    </a>
                </div>

                <!-- Search Bar -->
                <div class="header-search">
                    <form action="search.php" method="GET" class="search-form">
                        <input type="text" name="q" placeholder="ابحثي عن فستان أحلامك..." class="search-input" 
                               value="<?php echo isset($_GET['q']) ? htmlspecialchars($_GET['q']) : ''; ?>">
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>

                <!-- Header Actions -->
                <div class="header-actions">
                    
                    <!-- Wishlist -->
                    <div class="header-action">
                        <a href="wishlist.php" class="action-link">
                            <i class="fas fa-heart"></i>
                            <span class="action-text">المفضلة</span>
                            <span class="action-count wishlist-count">0</span>
                        </a>
                    </div>

                    <!-- Cart -->
                    <div class="header-action">
                        <a href="cart.php" class="action-link">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="action-text">السلة</span>
                            <span class="action-count cart-count"><?php echo $cart_count ?? 0; ?></span>
                        </a>
                    </div>

                    <!-- Account -->
                    <div class="header-action account-dropdown">
                        <button class="action-link dropdown-toggle">
                            <i class="fas fa-user"></i>
                            <span class="action-text">حسابي</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a href="login.php" class="dropdown-item">
                                <i class="fas fa-sign-in-alt"></i>
                                <span>تسجيل الدخول</span>
                            </a>
                            <a href="register.php" class="dropdown-item">
                                <i class="fas fa-user-plus"></i>
                                <span>إنشاء حساب</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="orders.php" class="dropdown-item">
                                <i class="fas fa-box"></i>
                                <span>طلباتي</span>
                            </a>
                            <a href="profile.php" class="dropdown-item">
                                <i class="fas fa-user-edit"></i>
                                <span>الملف الشخصي</span>
                            </a>
                        </div>
                    </div>

                </div>

                <!-- Mobile Menu Toggle -->
                <div class="mobile-menu-toggle">
                    <button class="menu-toggle-btn">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>

            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="main-navigation">
        <div class="container">
            <div class="nav-content">
                
                <!-- Categories Menu -->
                <div class="categories-menu">
                    <button class="categories-toggle">
                        <i class="fas fa-bars"></i>
                        <span>جميع الأقسام</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="categories-dropdown">
                        <?php
                        // جلب الأقسام للقائمة
                        if (isset($category)) {
                            $nav_categories = $category->get_main_categories()->fetchAll(PDO::FETCH_ASSOC);
                            foreach ($nav_categories as $nav_cat):
                        ?>
                        <div class="category-item">
                            <a href="category.php?slug=<?php echo $nav_cat['slug']; ?>" class="category-link">
                                <i class="fas fa-tag"></i>
                                <span><?php echo $nav_cat['name']; ?></span>
                            </a>
                            <?php
                            // التحقق من وجود أقسام فرعية
                            if ($category->has_subcategories($nav_cat['id'])):
                                $subcategories = $category->get_subcategories($nav_cat['id'])->fetchAll(PDO::FETCH_ASSOC);
                            ?>
                            <div class="subcategories">
                                <?php foreach ($subcategories as $subcat): ?>
                                <a href="category.php?slug=<?php echo $subcat['slug']; ?>" class="subcategory-link">
                                    <?php echo $subcat['name']; ?>
                                </a>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php 
                            endforeach;
                        }
                        ?>
                    </div>
                </div>

                <!-- Main Menu -->
                <ul class="main-menu">
                    <li class="menu-item">
                        <a href="<?php echo SITE_URL; ?>" class="menu-link <?php echo is_active_page('index'); ?>">
                            الرئيسية
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="products.php" class="menu-link <?php echo is_active_page('products'); ?>">
                            جميع المنتجات
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="products.php?featured=1" class="menu-link">
                            المنتجات المميزة
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="offers.php" class="menu-link <?php echo is_active_page('offers'); ?>">
                            العروض الخاصة
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="about.php" class="menu-link <?php echo is_active_page('about'); ?>">
                            من نحن
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="contact.php" class="menu-link <?php echo is_active_page('contact'); ?>">
                            اتصلي بنا
                        </a>
                    </li>
                </ul>

                <!-- Special Offers Banner -->
                <div class="special-offer">
                    <i class="fas fa-gift"></i>
                    <span>شحن مجاني للطلبات أكثر من 500 ريال</span>
                </div>

            </div>
        </div>
    </nav>

    <!-- Mobile Navigation -->
    <div class="mobile-nav-overlay"></div>
    <div class="mobile-navigation">
        <div class="mobile-nav-header">
            <h3>القائمة</h3>
            <button class="mobile-nav-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mobile-nav-content">
            <ul class="mobile-menu">
                <li><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                <li><a href="products.php">جميع المنتجات</a></li>
                <li><a href="products.php?featured=1">المنتجات المميزة</a></li>
                <li><a href="offers.php">العروض الخاصة</a></li>
                <li><a href="about.php">من نحن</a></li>
                <li><a href="contact.php">اتصلي بنا</a></li>
            </ul>
            
            <div class="mobile-nav-categories">
                <h4>الأقسام</h4>
                <ul class="mobile-categories">
                    <?php
                    if (isset($nav_categories)) {
                        foreach ($nav_categories as $nav_cat):
                    ?>
                    <li>
                        <a href="category.php?slug=<?php echo $nav_cat['slug']; ?>">
                            <?php echo $nav_cat['name']; ?>
                        </a>
                    </li>
                    <?php 
                        endforeach;
                    }
                    ?>
                </ul>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        
        <!-- Breadcrumb -->
        <?php if (isset($breadcrumb) && !empty($breadcrumb)): ?>
        <div class="breadcrumb-section">
            <div class="container">
                <nav class="breadcrumb">
                    <a href="<?php echo SITE_URL; ?>" class="breadcrumb-item">الرئيسية</a>
                    <?php foreach ($breadcrumb as $item): ?>
                    <?php if (isset($item['url'])): ?>
                    <a href="<?php echo $item['url']; ?>" class="breadcrumb-item"><?php echo $item['name']; ?></a>
                    <?php else: ?>
                    <span class="breadcrumb-item active"><?php echo $item['name']; ?></span>
                    <?php endif; ?>
                    <?php endforeach; ?>
                </nav>
            </div>
        </div>
        <?php endif; ?>
