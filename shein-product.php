<?php
require_once 'config/database.php';
require_once 'includes/Product.php';
require_once 'includes/Category.php';
require_once 'includes/Cart.php';

// إنشاء كائنات الكلاسات
$product = new Product();
$category = new Category();
$cart = new Cart();

// الحصول على slug المنتج
$product_slug = $_GET['slug'] ?? '';

if (empty($product_slug)) {
    header('Location: shein-index.php');
    exit;
}

// جلب معلومات المنتج
$product_info = $product->get_by_slug($product_slug);
if (!$product_info) {
    header('Location: shein-index.php');
    exit;
}

$page_title = $product_info['title'] . ' - SHEIN Style';

// جلب صور المنتج
$product_images = $product->get_product_images($product_info['id']);

// جلب منتجات مشابهة
$related_products_stmt = $product->read_by_category($product_info['category_id'], 4);
$related_products = $related_products_stmt->fetchAll(PDO::FETCH_ASSOC);

// عدد العناصر في السلة
$cart_count = $cart->get_cart_count();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="css/shein-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body>

<!-- Header -->
<header class="shein-header">
    <div class="header-top">
        <div class="container">
            شحن مجاني للطلبات فوق 200 ريال | خصم 20% على أول طلب
        </div>
    </div>
    
    <div class="header-main">
        <div class="container">
            <div class="header-content">
                <a href="shein-index.php" class="logo">SHEIN</a>
                
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="ابحثي عن فساتين السهرة...">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                
                <div class="header-actions">
                    <a href="#" class="header-action">
                        <i class="fas fa-user"></i>
                    </a>
                    <a href="#" class="header-action">
                        <i class="fas fa-heart"></i>
                    </a>
                    <a href="shein-cart.php" class="header-action">
                        <i class="fas fa-shopping-bag"></i>
                        <?php if ($cart_count > 0): ?>
                        <span class="cart-count"><?php echo $cart_count; ?></span>
                        <?php endif; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Navigation -->
<nav class="shein-nav">
    <div class="container">
        <ul class="nav-links">
            <li><a href="shein-index.php" class="nav-link">الرئيسية</a></li>
            <li><a href="#" class="nav-link">فساتين سهرة</a></li>
            <li><a href="#" class="nav-link">فساتين كاجوال</a></li>
            <li><a href="#" class="nav-link">فساتين رسمية</a></li>
            <li><a href="#" class="nav-link">أحذية</a></li>
            <li><a href="#" class="nav-link">إكسسوارات</a></li>
            <li><a href="#" class="nav-link">تخفيضات</a></li>
        </ul>
    </div>
</nav>

<!-- Breadcrumb -->
<div class="breadcrumb">
    <div class="container">
        <a href="shein-index.php">الرئيسية</a>
        <span>/</span>
        <a href="shein-category.php?slug=<?php echo $product_info['category_slug']; ?>"><?php echo htmlspecialchars($product_info['category_name']); ?></a>
        <span>/</span>
        <span><?php echo htmlspecialchars($product_info['title']); ?></span>
    </div>
</div>

<!-- Product Details -->
<section class="product-details">
    <div class="container">
        <div class="product-layout">
            <!-- Product Images -->
            <div class="product-gallery">
                <div class="main-image">
                    <?php if ($product_info['featured_image']): ?>
                        <img id="mainImage" src="<?php echo UPLOAD_URL . $product_info['featured_image']; ?>" alt="<?php echo htmlspecialchars($product_info['title']); ?>">
                    <?php else: ?>
                        <div class="no-image">
                            <i class="fas fa-image"></i>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if (!empty($product_images)): ?>
                <div class="image-thumbnails">
                    <?php if ($product_info['featured_image']): ?>
                        <img class="thumbnail active" src="<?php echo UPLOAD_URL . $product_info['featured_image']; ?>" alt="صورة رئيسية">
                    <?php endif; ?>
                    <?php foreach ($product_images as $img): ?>
                        <img class="thumbnail" src="<?php echo UPLOAD_URL . $img['image_path']; ?>" alt="صورة المنتج">
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Product Info -->
            <div class="product-info-section">
                <h1 class="product-title"><?php echo htmlspecialchars($product_info['title']); ?></h1>
                
                <div class="product-rating">
                    <div class="stars">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <span class="rating-text">(4.8) 127 تقييم</span>
                </div>
                
                <div class="product-price-section">
                    <?php if ($product_info['sale_price'] && $product_info['sale_price'] > 0): ?>
                        <span class="current-price"><?php echo format_price($product_info['sale_price']); ?></span>
                        <span class="original-price"><?php echo format_price($product_info['price']); ?></span>
                        <?php 
                        $discount = round((($product_info['price'] - $product_info['sale_price']) / $product_info['price']) * 100);
                        if ($discount > 0): ?>
                            <span class="discount-badge">خصم <?php echo $discount; ?>%</span>
                        <?php endif; ?>
                    <?php else: ?>
                        <span class="current-price"><?php echo format_price($product_info['price']); ?></span>
                    <?php endif; ?>
                </div>
                
                <div class="stock-status">
                    <?php if ($product_info['stock_status'] == 'in_stock'): ?>
                        <span class="in-stock"><i class="fas fa-check"></i> متوفر في المخزن</span>
                    <?php else: ?>
                        <span class="out-of-stock"><i class="fas fa-times"></i> نفذ من المخزن</span>
                    <?php endif; ?>
                </div>
                
                <?php if ($product_info['description']): ?>
                <div class="product-description">
                    <h3>وصف المنتج</h3>
                    <p><?php echo nl2br(htmlspecialchars($product_info['description'])); ?></p>
                </div>
                <?php endif; ?>
                
                <div class="product-actions-section">
                    <div class="quantity-selector">
                        <label>الكمية:</label>
                        <div class="quantity-controls">
                            <button class="qty-btn minus">-</button>
                            <input type="number" class="qty-input" value="1" min="1" max="10">
                            <button class="qty-btn plus">+</button>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <?php if ($product_info['stock_status'] == 'in_stock'): ?>
                        <button class="add-to-cart-btn" data-product-id="<?php echo $product_info['id']; ?>">
                            <i class="fas fa-shopping-cart"></i>
                            أضيفي للسلة
                        </button>
                        <?php else: ?>
                        <button class="add-to-cart-btn" disabled>
                            <i class="fas fa-times"></i>
                            نفذ من المخزن
                        </button>
                        <?php endif; ?>
                        
                        <button class="wishlist-btn">
                            <i class="fas fa-heart"></i>
                            إضافة للمفضلة
                        </button>
                    </div>
                </div>
                
                <div class="product-features">
                    <div class="feature">
                        <i class="fas fa-truck"></i>
                        <span>شحن مجاني فوق 200 ريال</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-undo"></i>
                        <span>إرجاع مجاني خلال 30 يوم</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-shield-alt"></i>
                        <span>ضمان الجودة</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Products -->
<?php if (!empty($related_products)): ?>
<section class="related-products">
    <div class="container">
        <h2 class="section-title">منتجات مشابهة</h2>
        
        <div class="products-grid">
            <?php foreach ($related_products as $prod): ?>
            <?php if ($prod['id'] != $product_info['id']): ?>
            <div class="shein-product-card">
                <div class="product-image">
                    <?php if ($prod['featured_image']): ?>
                        <img src="<?php echo UPLOAD_URL . $prod['featured_image']; ?>" alt="<?php echo htmlspecialchars($prod['title']); ?>" loading="lazy">
                    <?php else: ?>
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                            <i class="fas fa-image" style="font-size: 48px;"></i>
                        </div>
                    <?php endif; ?>
                    
                    <div class="product-badges">
                        <?php if ($prod['is_featured']): ?>
                            <span class="badge new">مميز</span>
                        <?php endif; ?>
                        
                        <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                            <span class="badge sale">خصم</span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="product-info">
                    <h3 class="product-title">
                        <a href="shein-product.php?slug=<?php echo $prod['slug']; ?>" style="text-decoration: none; color: inherit;">
                            <?php echo htmlspecialchars($prod['title']); ?>
                        </a>
                    </h3>
                    
                    <div class="product-price">
                        <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                            <span class="current-price"><?php echo format_price($prod['sale_price']); ?></span>
                            <span class="original-price"><?php echo format_price($prod['price']); ?></span>
                        <?php else: ?>
                            <span class="current-price"><?php echo format_price($prod['price']); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <button class="add-to-cart" data-product-id="<?php echo $prod['id']; ?>">
                        أضيفي للسلة
                    </button>
                </div>
            </div>
            <?php endif; ?>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Footer -->
<footer style="background: #000; color: #fff; padding: 40px 0; text-align: center;">
    <div class="container">
        <div style="margin-bottom: 20px;">
            <h3 style="font-size: 24px; font-weight: 900; margin-bottom: 10px;">SHEIN</h3>
            <p style="color: #ccc;">أجمل فساتين السهرة بأسعار لا تقاوم</p>
        </div>
        
        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <a href="#" style="color: #ccc; text-decoration: none;">من نحن</a>
            <a href="#" style="color: #ccc; text-decoration: none;">اتصل بنا</a>
            <a href="#" style="color: #ccc; text-decoration: none;">الشحن والإرجاع</a>
            <a href="#" style="color: #ccc; text-decoration: none;">الخصوصية</a>
        </div>
        
        <p style="color: #666; font-size: 12px;">© 2025 SHEIN Style. جميع الحقوق محفوظة.</p>
    </div>
</footer>

<script>
// Image gallery
document.querySelectorAll('.thumbnail').forEach(thumb => {
    thumb.addEventListener('click', function() {
        document.querySelectorAll('.thumbnail').forEach(t => t.classList.remove('active'));
        this.classList.add('active');
        document.getElementById('mainImage').src = this.src;
    });
});

// Quantity controls
document.querySelector('.minus').addEventListener('click', function() {
    const input = document.querySelector('.qty-input');
    if (input.value > 1) {
        input.value = parseInt(input.value) - 1;
    }
});

document.querySelector('.plus').addEventListener('click', function() {
    const input = document.querySelector('.qty-input');
    if (input.value < 10) {
        input.value = parseInt(input.value) + 1;
    }
});

// Add to cart
document.querySelector('.add-to-cart-btn')?.addEventListener('click', function() {
    const productId = this.getAttribute('data-product-id');
    const quantity = document.querySelector('.qty-input').value;
    
    if (productId) {
        this.innerHTML = '<i class="fas fa-check"></i> تمت الإضافة!';
        this.style.background = '#2ed573';
        setTimeout(() => {
            this.innerHTML = '<i class="fas fa-shopping-cart"></i> أضيفي للسلة';
            this.style.background = '#000';
        }, 2000);
    }
});
</script>

</body>
</html>
