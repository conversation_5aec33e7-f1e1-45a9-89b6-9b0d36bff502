<?php
/**
 * دالات إدارة إعدادات الموقع
 */

// تضمين ملف قاعدة البيانات
require_once __DIR__ . '/../config/database.php';

// إنشاء اتصال قاعدة البيانات
$database = new Database();
$conn = $database->getConnection();

// دالة جلب إعداد معين
function getSetting($key, $default = '') {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();

        return $result ? $result['setting_value'] : $default;
    } catch (Exception $e) {
        return $default;
    }
}

// دالة جلب جميع الإعدادات
function getAllSettings() {
    global $conn;

    $settings = [];

    try {
        $stmt = $conn->query("SELECT setting_key, setting_value FROM settings");
        while ($row = $stmt->fetch()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
    } catch (Exception $e) {
        // الجدول غير موجود أو خطأ آخر
    }

    return $settings;
}

// دالة حفظ إعداد
function saveSetting($key, $value) {
    global $conn;

    try {
        // إنشاء الجدول إذا لم يكن موجوداً
        $conn->exec("CREATE TABLE IF NOT EXISTS settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)
                               ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = CURRENT_TIMESTAMP");
        $stmt->execute([$key, $value, $value]);

        return true;
    } catch (Exception $e) {
        return false;
    }
}

// جلب الإعدادات الأساسية للموقع
$site_settings = getAllSettings();

// تعيين القيم الافتراضية
$site_name = $site_settings['site_name'] ?? 'متجر فساتين السهرة';
$site_description = $site_settings['site_description'] ?? 'أجمل وأحدث تصاميم فساتين السهرة الأنيقة والراقية';
$site_keywords = $site_settings['site_keywords'] ?? 'فساتين سهرة, فساتين أنيقة, فساتين راقية, فساتين مناسبات, فساتين زفاف';
$site_logo = $site_settings['site_logo'] ?? '';
$contact_email = $site_settings['contact_email'] ?? '<EMAIL>';
$contact_phone = $site_settings['contact_phone'] ?? '+966 50 123 4567';
$facebook_url = $site_settings['facebook_url'] ?? '#';
$instagram_url = $site_settings['instagram_url'] ?? '#';
$twitter_url = $site_settings['twitter_url'] ?? '#';

// دالة للحصول على مسار الشعار
function getLogoPath() {
    global $site_logo;
    
    if (!empty($site_logo) && file_exists('uploads/' . $site_logo)) {
        return 'uploads/' . $site_logo;
    } elseif (file_exists('images/logo.png')) {
        return 'images/logo.png';
    } else {
        return ''; // بدون شعار
    }
}

// دالة للحصول على اسم الموقع
function getSiteName() {
    global $site_name;
    return $site_name;
}

// دالة للحصول على وصف الموقع
function getSiteDescription() {
    global $site_description;
    return $site_description;
}

// دالة للحصول على الكلمات المفتاحية
function getSiteKeywords() {
    global $site_keywords;
    return $site_keywords;
}
?>
