<?php
require_once 'auth.php';
require_once '../config/database.php';

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$success_message = '';
$error_message = '';

// معالجة إضافة لون جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_color'])) {
    $name = trim($_POST['name'] ?? '');
    $hex_code = trim($_POST['hex_code'] ?? '');
    
    if (empty($name)) {
        $error_message = 'اسم اللون مطلوب';
    } else {
        try {
            $stmt = $conn->prepare("INSERT INTO colors (name, hex_code, is_active) VALUES (?, ?, 1)");
            $stmt->execute([$name, $hex_code]);
            $success_message = 'تم إضافة اللون بنجاح';
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء إضافة اللون';
        }
    }
}

// معالجة تعديل لون
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_color'])) {
    $id = intval($_POST['color_id']);
    $name = trim($_POST['name'] ?? '');
    $hex_code = trim($_POST['hex_code'] ?? '');
    
    if (empty($name)) {
        $error_message = 'اسم اللون مطلوب';
    } else {
        try {
            $stmt = $conn->prepare("UPDATE colors SET name = ?, hex_code = ? WHERE id = ?");
            $stmt->execute([$name, $hex_code, $id]);
            $success_message = 'تم تحديث اللون بنجاح';
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء تحديث اللون';
        }
    }
}

// معالجة حذف لون
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_color'])) {
    $id = intval($_POST['color_id']);
    try {
        $stmt = $conn->prepare("UPDATE colors SET is_active = 0 WHERE id = ?");
        $stmt->execute([$id]);
        $success_message = 'تم حذف اللون بنجاح';
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء حذف اللون';
    }
}

// جلب جميع الألوان
try {
    $stmt = $conn->query("SELECT * FROM colors WHERE is_active = 1 ORDER BY name");
    $colors = $stmt->fetchAll();
} catch (Exception $e) {
    $colors = [];
    $error_message = 'حدث خطأ في جلب الألوان';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الألوان - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            z-index: 1000;
        }
        
        .sidebar .logo {
            padding: 2rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .logo h3 {
            color: white;
            margin: 0;
            font-weight: 600;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .sidebar-nav .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .sidebar-nav .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .sidebar-nav .nav-link:hover,
        .sidebar-nav .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .sidebar-nav .nav-link i {
            width: 20px;
            margin-left: 0.75rem;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header h2 {
            margin: 0;
            color: #333;
            font-weight: 600;
        }
        
        .card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .color-preview {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid #ddd;
            display: inline-block;
            margin-left: 0.5rem;
        }
        
        .color-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border: 1px solid #eee;
            border-radius: 10px;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .color-item:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
        }
        
        .color-info {
            display: flex;
            align-items: center;
        }
        
        .btn-action {
            padding: 0.25rem 0.5rem;
            border-radius: 5px;
            font-size: 0.8rem;
            margin: 0 0.1rem;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-crown me-2"></i>لوحة التحكم</h3>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
            <div class="nav-item">
                <a href="products.php" class="nav-link">
                    <i class="fas fa-tshirt"></i>
                    إدارة المنتجات
                </a>
            </div>
            <div class="nav-item">
                <a href="add-product.php" class="nav-link">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="colors.php" class="nav-link active">
                    <i class="fas fa-palette"></i>
                    إدارة الألوان
                </a>
            </div>
            <div class="nav-item">
                <a href="sizes.php" class="nav-link">
                    <i class="fas fa-ruler"></i>
                    إدارة المقاسات
                </a>
            </div>
            <div class="nav-item">
                <a href="logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h2><i class="fas fa-palette me-2"></i>إدارة الألوان</h2>
        </div>
        
        <!-- Alerts -->
        <?php if (!empty($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <div class="row">
            <!-- Add Color Form -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>إضافة لون جديد</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم اللون *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label for="hex_code" class="form-label">كود اللون</label>
                                <input type="color" class="form-control form-control-color" id="hex_code" name="hex_code">
                            </div>
                            <button type="submit" name="add_color" class="btn btn-primary w-100">
                                <i class="fas fa-save me-2"></i>إضافة اللون
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Colors List -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>الألوان المتوفرة (<?php echo count($colors); ?>)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($colors)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-palette fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">لا توجد ألوان</h6>
                                <p class="text-muted">ابدأ بإضافة أول لون للمنتجات</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($colors as $color): ?>
                                <div class="color-item">
                                    <div class="color-info">
                                        <?php if ($color['hex_code']): ?>
                                            <div class="color-preview" style="background-color: <?php echo $color['hex_code']; ?>"></div>
                                        <?php endif; ?>
                                        <div>
                                            <strong><?php echo htmlspecialchars($color['name']); ?></strong>
                                            <?php if ($color['hex_code']): ?>
                                                <br><small class="text-muted"><?php echo $color['hex_code']; ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-outline-primary btn-action" 
                                                onclick="editColor(<?php echo $color['id']; ?>, '<?php echo htmlspecialchars($color['name']); ?>', '<?php echo $color['hex_code']; ?>')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger btn-action" 
                                                onclick="deleteColor(<?php echo $color['id']; ?>, '<?php echo htmlspecialchars($color['name']); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Color Modal -->
    <div class="modal fade" id="editColorModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل اللون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="color_id" id="editColorId">
                        <div class="mb-3">
                            <label for="editColorName" class="form-label">اسم اللون *</label>
                            <input type="text" class="form-control" id="editColorName" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="editColorHex" class="form-label">كود اللون</label>
                            <input type="color" class="form-control form-control-color" id="editColorHex" name="hex_code">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" name="edit_color" class="btn btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Color Modal -->
    <div class="modal fade" id="deleteColorModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف اللون "<span id="deleteColorName"></span>"؟</p>
                    <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="color_id" id="deleteColorId">
                        <button type="submit" name="delete_color" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editColor(id, name, hexCode) {
            document.getElementById('editColorId').value = id;
            document.getElementById('editColorName').value = name;
            document.getElementById('editColorHex').value = hexCode || '#000000';
            new bootstrap.Modal(document.getElementById('editColorModal')).show();
        }

        function deleteColor(id, name) {
            document.getElementById('deleteColorId').value = id;
            document.getElementById('deleteColorName').textContent = name;
            new bootstrap.Modal(document.getElementById('deleteColorModal')).show();
        }
    </script>
</body>
</html>
