<?php
require_once 'config/database.php';
require_once 'includes/Product.php';
require_once 'includes/Category.php';
require_once 'includes/Cart.php';

// إنشاء كائنات الكلاسات
$product = new Product();
$category = new Category();
$cart = new Cart();

$page_title = 'سلة التسوق - SHEIN Style';

// جلب عناصر السلة
$cart_items = $cart->get_cart_items();
$cart_total = $cart->get_cart_total();
$cart_count = $cart->get_cart_count();

// معالجة تحديث الكمية
if ($_POST['action'] ?? '' == 'update_quantity') {
    $cart_id = $_POST['cart_id'] ?? 0;
    $quantity = $_POST['quantity'] ?? 1;
    
    if ($cart_id && $quantity > 0) {
        $cart->update_quantity($cart_id, $quantity);
        header('Location: shein-cart.php');
        exit;
    }
}

// معالجة حذف عنصر
if ($_POST['action'] ?? '' == 'remove_item') {
    $cart_id = $_POST['cart_id'] ?? 0;
    
    if ($cart_id) {
        $cart->remove_item($cart_id);
        header('Location: shein-cart.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="css/shein-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body>

<!-- Header -->
<header class="shein-header">
    <div class="header-top">
        <div class="container">
            شحن مجاني للطلبات فوق 200 ريال | خصم 20% على أول طلب
        </div>
    </div>
    
    <div class="header-main">
        <div class="container">
            <div class="header-content">
                <a href="shein-index.php" class="logo">SHEIN</a>
                
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="ابحثي عن فساتين السهرة...">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                
                <div class="header-actions">
                    <a href="#" class="header-action">
                        <i class="fas fa-user"></i>
                    </a>
                    <a href="#" class="header-action">
                        <i class="fas fa-heart"></i>
                    </a>
                    <a href="shein-cart.php" class="header-action">
                        <i class="fas fa-shopping-bag"></i>
                        <?php if ($cart_count > 0): ?>
                        <span class="cart-count"><?php echo $cart_count; ?></span>
                        <?php endif; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Navigation -->
<nav class="shein-nav">
    <div class="container">
        <ul class="nav-links">
            <li><a href="shein-index.php" class="nav-link">الرئيسية</a></li>
            <li><a href="#" class="nav-link">فساتين سهرة</a></li>
            <li><a href="#" class="nav-link">فساتين كاجوال</a></li>
            <li><a href="#" class="nav-link">فساتين رسمية</a></li>
            <li><a href="#" class="nav-link">أحذية</a></li>
            <li><a href="#" class="nav-link">إكسسوارات</a></li>
            <li><a href="#" class="nav-link">تخفيضات</a></li>
        </ul>
    </div>
</nav>

<!-- Breadcrumb -->
<div class="breadcrumb">
    <div class="container">
        <a href="shein-index.php">الرئيسية</a>
        <span>/</span>
        <span>سلة التسوق</span>
    </div>
</div>

<!-- Cart Content -->
<section class="cart-section">
    <div class="container">
        <h1 class="page-title">سلة التسوق</h1>
        
        <?php if (!empty($cart_items)): ?>
        <div class="cart-layout">
            <!-- Cart Items -->
            <div class="cart-items">
                <div class="cart-header">
                    <span>المنتج</span>
                    <span>السعر</span>
                    <span>الكمية</span>
                    <span>المجموع</span>
                    <span></span>
                </div>
                
                <?php foreach ($cart_items as $item): ?>
                <div class="cart-item">
                    <div class="item-product">
                        <div class="item-image">
                            <?php if ($item['featured_image']): ?>
                                <img src="<?php echo UPLOAD_URL . $item['featured_image']; ?>" alt="<?php echo htmlspecialchars($item['title']); ?>">
                            <?php else: ?>
                                <div class="no-image">
                                    <i class="fas fa-image"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="item-details">
                            <h3><?php echo htmlspecialchars($item['title']); ?></h3>
                            <p class="item-sku">كود المنتج: <?php echo $item['product_id']; ?></p>
                        </div>
                    </div>
                    
                    <div class="item-price">
                        <?php echo format_price($item['price']); ?>
                    </div>
                    
                    <div class="item-quantity">
                        <form method="post" class="quantity-form">
                            <input type="hidden" name="action" value="update_quantity">
                            <input type="hidden" name="cart_id" value="<?php echo $item['id']; ?>">
                            <div class="quantity-controls">
                                <button type="button" class="qty-btn minus">-</button>
                                <input type="number" name="quantity" class="qty-input" value="<?php echo $item['quantity']; ?>" min="1" max="10">
                                <button type="button" class="qty-btn plus">+</button>
                            </div>
                            <button type="submit" class="update-btn">تحديث</button>
                        </form>
                    </div>
                    
                    <div class="item-total">
                        <?php echo format_price($item['price'] * $item['quantity']); ?>
                    </div>
                    
                    <div class="item-remove">
                        <form method="post">
                            <input type="hidden" name="action" value="remove_item">
                            <input type="hidden" name="cart_id" value="<?php echo $item['id']; ?>">
                            <button type="submit" class="remove-btn" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Cart Summary -->
            <div class="cart-summary">
                <h3>ملخص الطلب</h3>
                
                <div class="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span><?php echo format_price($cart_total); ?></span>
                </div>
                
                <div class="summary-row">
                    <span>الشحن:</span>
                    <span><?php echo $cart_total >= 200 ? 'مجاني' : format_price(25); ?></span>
                </div>
                
                <div class="summary-row">
                    <span>الضريبة:</span>
                    <span><?php echo format_price($cart_total * 0.15); ?></span>
                </div>
                
                <div class="summary-total">
                    <span>المجموع الكلي:</span>
                    <span><?php 
                        $shipping = $cart_total >= 200 ? 0 : 25;
                        $tax = $cart_total * 0.15;
                        $total = $cart_total + $shipping + $tax;
                        echo format_price($total); 
                    ?></span>
                </div>
                
                <div class="promo-code">
                    <input type="text" placeholder="كود الخصم" class="promo-input">
                    <button class="promo-btn">تطبيق</button>
                </div>
                
                <button class="checkout-btn">
                    <i class="fas fa-lock"></i>
                    إتمام الطلب
                </button>
                
                <div class="payment-methods">
                    <p>طرق الدفع المقبولة:</p>
                    <div class="payment-icons">
                        <i class="fab fa-cc-visa"></i>
                        <i class="fab fa-cc-mastercard"></i>
                        <i class="fab fa-cc-paypal"></i>
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="cart-actions">
            <a href="shein-index.php" class="continue-shopping">
                <i class="fas fa-arrow-right"></i>
                متابعة التسوق
            </a>
            
            <button class="clear-cart" onclick="if(confirm('هل أنت متأكدة من حذف جميع العناصر؟')) { /* clear cart logic */ }">
                <i class="fas fa-trash"></i>
                إفراغ السلة
            </button>
        </div>
        
        <?php else: ?>
        <div class="empty-cart">
            <i class="fas fa-shopping-bag" style="font-size: 64px; color: #ccc; margin-bottom: 20px;"></i>
            <h3>سلة التسوق فارغة</h3>
            <p>لم تقومي بإضافة أي منتجات بعد</p>
            <a href="shein-index.php" class="shein-btn">ابدئي التسوق</a>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Footer -->
<footer style="background: #000; color: #fff; padding: 40px 0; text-align: center;">
    <div class="container">
        <div style="margin-bottom: 20px;">
            <h3 style="font-size: 24px; font-weight: 900; margin-bottom: 10px;">SHEIN</h3>
            <p style="color: #ccc;">أجمل فساتين السهرة بأسعار لا تقاوم</p>
        </div>
        
        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <a href="#" style="color: #ccc; text-decoration: none;">من نحن</a>
            <a href="#" style="color: #ccc; text-decoration: none;">اتصل بنا</a>
            <a href="#" style="color: #ccc; text-decoration: none;">الشحن والإرجاع</a>
            <a href="#" style="color: #ccc; text-decoration: none;">الخصوصية</a>
        </div>
        
        <p style="color: #666; font-size: 12px;">© 2025 SHEIN Style. جميع الحقوق محفوظة.</p>
    </div>
</footer>

<script>
// Quantity controls
document.querySelectorAll('.minus').forEach(btn => {
    btn.addEventListener('click', function() {
        const input = this.parentNode.querySelector('.qty-input');
        if (input.value > 1) {
            input.value = parseInt(input.value) - 1;
        }
    });
});

document.querySelectorAll('.plus').forEach(btn => {
    btn.addEventListener('click', function() {
        const input = this.parentNode.querySelector('.qty-input');
        if (input.value < 10) {
            input.value = parseInt(input.value) + 1;
        }
    });
});

// Auto-submit quantity forms on change
document.querySelectorAll('.qty-input').forEach(input => {
    input.addEventListener('change', function() {
        this.closest('.quantity-form').submit();
    });
});
</script>

</body>
</html>
