    </main> <!-- End main-content -->

    <!-- Footer -->
    <footer class="main-footer">

        <!-- Main Footer Content -->
        <div class="footer-content">
            <div class="container">
                <div class="footer-grid">
                    
                    <!-- Company Info -->
                    <div class="footer-column">
                        <div class="footer-logo">
                            <img src="<?php echo SITE_URL; ?>/images/logo-white.png" alt="متجر فساتين السهرة" class="footer-logo-img">
                        </div>
                        <p class="footer-description">
                            متجرك المتخصص في أجمل وأحدث تصاميم فساتين السهرة الأنيقة والراقية. 
                            نقدم لك مجموعة متنوعة من الفساتين لجميع المناسبات الخاصة.
                        </p>
                        <div class="footer-social">
                            <h4>تابعينا على</h4>
                            <div class="social-links">
                                <a href="#" class="social-link instagram">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                <a href="#" class="social-link twitter">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="social-link facebook">
                                    <i class="fab fa-facebook"></i>
                                </a>
                                <a href="#" class="social-link whatsapp">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                                <a href="#" class="social-link youtube">
                                    <i class="fab fa-youtube"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="footer-column">
                        <h4 class="footer-title">روابط سريعة</h4>
                        <ul class="footer-links">
                            <li><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                            <li><a href="products.php">جميع المنتجات</a></li>
                            <li><a href="products.php?featured=1">المنتجات المميزة</a></li>
                            <li><a href="offers.php">العروض الخاصة</a></li>
                            <li><a href="about.php">من نحن</a></li>
                            <li><a href="contact.php">اتصلي بنا</a></li>
                            <li><a href="blog.php">المدونة</a></li>
                        </ul>
                    </div>

                    <!-- Categories -->
                    <div class="footer-column">
                        <h4 class="footer-title">الأقسام</h4>
                        <ul class="footer-links">
                            <?php
                            if (isset($category)) {
                                $footer_categories = $category->get_main_categories()->fetchAll(PDO::FETCH_ASSOC);
                                foreach ($footer_categories as $footer_cat):
                            ?>
                            <li>
                                <a href="category.php?slug=<?php echo $footer_cat['slug']; ?>">
                                    <?php echo $footer_cat['name']; ?>
                                </a>
                            </li>
                            <?php 
                                endforeach;
                            }
                            ?>
                        </ul>
                    </div>

                    <!-- Customer Service -->
                    <div class="footer-column">
                        <h4 class="footer-title">خدمة العملاء</h4>
                        <ul class="footer-links">
                            <li><a href="account.php">حسابي</a></li>
                            <li><a href="orders.php">تتبع الطلب</a></li>
                            <li><a href="wishlist.php">قائمة المفضلة</a></li>
                            <li><a href="faq.php">الأسئلة الشائعة</a></li>
                            <li><a href="shipping.php">معلومات الشحن</a></li>
                            <li><a href="returns.php">سياسة الإرجاع</a></li>
                            <li><a href="privacy.php">سياسة الخصوصية</a></li>
                            <li><a href="terms.php">الشروط والأحكام</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Methods -->
        <div class="payment-section">
            <div class="container">
                <div class="payment-content">
                    <div class="payment-text">
                        <h4>طرق الدفع المتاحة</h4>
                        <p>ندعم جميع طرق الدفع الآمنة والموثوقة</p>
                    </div>
                    <div class="payment-methods">
                        <div class="payment-method">
                            <img src="<?php echo SITE_URL; ?>/images/payments/visa.png" alt="Visa">
                        </div>
                        <div class="payment-method">
                            <img src="<?php echo SITE_URL; ?>/images/payments/mastercard.png" alt="Mastercard">
                        </div>
                        <div class="payment-method">
                            <img src="<?php echo SITE_URL; ?>/images/payments/mada.png" alt="مدى">
                        </div>
                        <div class="payment-method">
                            <img src="<?php echo SITE_URL; ?>/images/payments/apple-pay.png" alt="Apple Pay">
                        </div>
                        <div class="payment-method">
                            <img src="<?php echo SITE_URL; ?>/images/payments/stc-pay.png" alt="STC Pay">
                        </div>
                        <div class="payment-method">
                            <img src="<?php echo SITE_URL; ?>/images/payments/tabby.png" alt="Tabby">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="footer-bottom">
            <div class="container">
                <div class="footer-bottom-content">
                    <div class="copyright">
                        <p>&copy; <?php echo date('Y'); ?> متجر فساتين السهرة. جميع الحقوق محفوظة.</p>
                    </div>
                    <div class="footer-bottom-links">
                        <a href="privacy.php">سياسة الخصوصية</a>
                        <a href="terms.php">الشروط والأحكام</a>
                        <a href="sitemap.php">خريطة الموقع</a>
                    </div>
                </div>
            </div>
        </div>

    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Quick View Modal -->
    <div class="modal" id="quickViewModal">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>عرض سريع</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="quickViewContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner">
            <div class="bounce1"></div>
            <div class="bounce2"></div>
            <div class="bounce3"></div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- JavaScript Files -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="<?php echo SITE_URL; ?>/js/main.js"></script>

    <!-- Custom Scripts -->
    <script>
        // Site URL for JavaScript
        const SITE_URL = '<?php echo SITE_URL; ?>';
        
        // Initialize cart count
        let cartCount = <?php echo $cart_count ?? 0; ?>;
        
        // Update cart count display
        function updateCartCount(count) {
            cartCount = count;
            document.querySelector('.cart-count').textContent = count;
        }
        
        // Add to cart function
        function addToCart(productId, colorId = null, sizeId = null, quantity = 1) {
            showLoading();
            
            fetch('ajax/add-to-cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    color_id: colorId,
                    size_id: sizeId,
                    quantity: quantity
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    updateCartCount(data.cart_count);
                    showToast('تم إضافة المنتج إلى السلة بنجاح', 'success');
                } else {
                    showToast(data.message || 'حدث خطأ أثناء إضافة المنتج', 'error');
                }
            })
            .catch(error => {
                hideLoading();
                showToast('حدث خطأ في الشبكة', 'error');
            });
        }
        
        // Show loading
        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'flex';
        }
        
        // Hide loading
        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }
        
        // Show toast notification
        function showToast(message, type = 'info', duration = 3000) {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <div class="toast-content">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
                    <span>${message}</span>
                </div>
                <button class="toast-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            document.getElementById('toastContainer').appendChild(toast);
            
            // Auto remove
            setTimeout(() => {
                toast.remove();
            }, duration);
        }
    </script>

    <!-- Page-specific scripts -->
    <?php if (isset($page_scripts)): ?>
        <?php foreach ($page_scripts as $script): ?>
            <script src="<?php echo $script; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Google Analytics (replace with your tracking ID) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_TRACKING_ID');
    </script>

</body>
</html>
