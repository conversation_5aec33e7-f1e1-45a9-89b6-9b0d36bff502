<?php
require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/Product.php';

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

// معالجة الحذف
if (isset($_POST['delete_product']) && isset($_POST['product_id'])) {
    $product_id = (int)$_POST['product_id'];
    try {
        $stmt = $conn->prepare("UPDATE products SET is_active = 0 WHERE id = ?");
        $stmt->execute([$product_id]);
        $success_message = "تم حذف المنتج بنجاح";
    } catch (Exception $e) {
        $error_message = "حدث خطأ أثناء حذف المنتج";
    }
}

// معالجة البحث والفلترة
$search = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';
$status_filter = $_GET['status'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// بناء الاستعلام
$where_conditions = ["p.is_active = 1"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(p.title LIKE ? OR p.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($category_filter)) {
    $where_conditions[] = "p.category_id = ?";
    $params[] = $category_filter;
}

if (!empty($status_filter)) {
    $where_conditions[] = "p.stock_status = ?";
    $params[] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// عدد المنتجات الإجمالي
$count_query = "SELECT COUNT(*) as total FROM products p WHERE $where_clause";
$count_stmt = $conn->prepare($count_query);
$count_stmt->execute($params);
$total_products = $count_stmt->fetch()['total'];
$total_pages = ceil($total_products / $per_page);

// جلب المنتجات
$query = "SELECT p.*, c.name as category_name 
          FROM products p 
          LEFT JOIN categories c ON p.category_id = c.id 
          WHERE $where_clause 
          ORDER BY p.created_at DESC 
          LIMIT $per_page OFFSET $offset";

$stmt = $conn->prepare($query);
$stmt->execute($params);
$products = $stmt->fetchAll();

// جلب التصنيفات للفلترة
$categories_stmt = $conn->query("SELECT id, name FROM categories WHERE is_active = 1 ORDER BY name");
$categories = $categories_stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            z-index: 1000;
        }
        
        .sidebar .logo {
            padding: 2rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .logo h3 {
            color: white;
            margin: 0;
            font-weight: 600;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .sidebar-nav .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .sidebar-nav .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .sidebar-nav .nav-link:hover,
        .sidebar-nav .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .sidebar-nav .nav-link i {
            width: 20px;
            margin-left: 0.75rem;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header h2 {
            margin: 0;
            color: #333;
            font-weight: 600;
        }
        
        .filters-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .products-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 10px;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-in-stock {
            background: #d4edda;
            color: #155724;
        }
        
        .status-out-of-stock {
            background: #f8d7da;
            color: #721c24;
        }
        
        .btn-action {
            padding: 0.25rem 0.5rem;
            border-radius: 5px;
            font-size: 0.8rem;
            margin: 0 0.1rem;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            color: #333;
        }
        
        .pagination {
            justify-content: center;
            margin-top: 2rem;
        }
        
        .pagination .page-link {
            border-radius: 10px;
            margin: 0 0.2rem;
            border: none;
            color: #667eea;
        }
        
        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-crown me-2"></i>لوحة التحكم</h3>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
            <div class="nav-item">
                <a href="products.php" class="nav-link active">
                    <i class="fas fa-tshirt"></i>
                    إدارة المنتجات
                </a>
            </div>
            <div class="nav-item">
                <a href="add-product.php" class="nav-link">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="colors.php" class="nav-link">
                    <i class="fas fa-palette"></i>
                    إدارة الألوان
                </a>
            </div>
            <div class="nav-item">
                <a href="sizes.php" class="nav-link">
                    <i class="fas fa-ruler"></i>
                    إدارة المقاسات
                </a>
            </div>
            <div class="nav-item">
                <a href="logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-tshirt me-2"></i>إدارة المنتجات</h2>
                <a href="add-product.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                </a>
            </div>
        </div>
        
        <!-- Alerts -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <!-- Filters -->
        <div class="filters-card">
            <form method="GET" action="">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">البحث</label>
                        <input type="text" class="form-control" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="ابحث في المنتجات...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">التصنيف</label>
                        <select class="form-select" name="category">
                            <option value="">جميع التصنيفات</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>" 
                                        <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">حالة التوفر</label>
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="in_stock" <?php echo $status_filter == 'in_stock' ? 'selected' : ''; ?>>متوفر</option>
                            <option value="out_of_stock" <?php echo $status_filter == 'out_of_stock' ? 'selected' : ''; ?>>نفذ</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Products Table -->
        <div class="products-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">المنتجات (<?php echo number_format($total_products); ?>)</h5>
            </div>

            <?php if (empty($products)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-tshirt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد منتجات</h5>
                    <p class="text-muted">لم يتم العثور على منتجات تطابق معايير البحث</p>
                    <a href="add-product.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>اسم المنتج</th>
                                <th>التصنيف</th>
                                <th>السعر</th>
                                <th>حالة التوفر</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($products as $product): ?>
                                <tr>
                                    <td>
                                        <?php if ($product['featured_image']): ?>
                                            <img src="../uploads/<?php echo htmlspecialchars($product['featured_image']); ?>"
                                                 alt="<?php echo htmlspecialchars($product['title']); ?>"
                                                 class="product-image">
                                        <?php else: ?>
                                            <div class="product-image bg-light d-flex align-items-center justify-content-center">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($product['title']); ?></strong>
                                            <?php if ($product['is_featured']): ?>
                                                <span class="badge bg-warning text-dark ms-1">مميز</span>
                                            <?php endif; ?>
                                        </div>
                                        <small class="text-muted">#<?php echo $product['id']; ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?></td>
                                    <td>
                                        <?php if ($product['sale_price']): ?>
                                            <span class="text-decoration-line-through text-muted"><?php echo number_format($product['price'], 2); ?> ر.س</span><br>
                                            <strong class="text-danger"><?php echo number_format($product['sale_price'], 2); ?> ر.س</strong>
                                        <?php else: ?>
                                            <strong><?php echo number_format($product['price'], 2); ?> ر.س</strong>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="status-badge <?php echo $product['stock_status'] == 'in_stock' ? 'status-in-stock' : 'status-out-of-stock'; ?>">
                                            <?php echo $product['stock_status'] == 'in_stock' ? 'متوفر' : 'نفذ'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small><?php echo date('Y/m/d', strtotime($product['created_at'])); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="edit-product.php?id=<?php echo $product['id']; ?>"
                                               class="btn btn-outline-primary btn-action" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="product-images.php?id=<?php echo $product['id']; ?>"
                                               class="btn btn-outline-success btn-action" title="إدارة الصور">
                                                <i class="fas fa-images"></i>
                                            </a>
                                            <a href="../product.php?id=<?php echo $product['id']; ?>"
                                               class="btn btn-outline-info btn-action" title="عرض" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger btn-action"
                                                    title="حذف" onclick="confirmDelete(<?php echo $product['id']; ?>, '<?php echo htmlspecialchars($product['title']); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="صفحات المنتجات">
                        <ul class="pagination">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page-1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        السابق
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page+1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        التالي
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف المنتج "<span id="productName"></span>"؟</p>
                    <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="product_id" id="deleteProductId">
                        <button type="submit" name="delete_product" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(productId, productName) {
            document.getElementById('deleteProductId').value = productId;
            document.getElementById('productName').textContent = productName;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
</body>
</html>
