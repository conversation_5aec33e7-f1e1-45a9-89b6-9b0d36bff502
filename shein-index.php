<?php
require_once 'config/database.php';
require_once 'includes/Product.php';
require_once 'includes/Category.php';
require_once 'includes/Cart.php';

$page_title = 'SHEIN Style - فساتين السهرة';

// إنشاء كائنات الكلاسات
$product = new Product();
$category = new Category();
$cart = new Cart();

// جلب المنتجات المميزة
$featured_products_stmt = $product->read(8, 0, null, 1);
$featured_products = $featured_products_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب أحدث المنتجات
$latest_products_stmt = $product->read(8, 0);
$latest_products = $latest_products_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب الأقسام الرئيسية
$main_categories_stmt = $category->get_main_categories();
$main_categories = $main_categories_stmt->fetchAll(PDO::FETCH_ASSOC);

// عدد العناصر في السلة
$cart_count = $cart->get_cart_count();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="css/shein-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body>

<!-- Header -->
<header class="shein-header">
    <div class="header-top">
        <div class="container">
            شحن مجاني للطلبات فوق 200 ريال | خصم 20% على أول طلب
        </div>
    </div>
    
    <div class="header-main">
        <div class="container">
            <div class="header-content">
                <a href="shein-index.php" class="logo">SHEIN</a>
                
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="ابحثي عن فساتين السهرة...">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                
                <div class="header-actions">
                    <a href="#" class="header-action">
                        <i class="fas fa-user"></i>
                    </a>
                    <a href="#" class="header-action">
                        <i class="fas fa-heart"></i>
                    </a>
                    <a href="cart.php" class="header-action">
                        <i class="fas fa-shopping-bag"></i>
                        <?php if ($cart_count > 0): ?>
                        <span class="cart-count"><?php echo $cart_count; ?></span>
                        <?php endif; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Navigation -->
<nav class="shein-nav">
    <div class="container">
        <ul class="nav-links">
            <li><a href="shein-index.php" class="nav-link active">الرئيسية</a></li>
            <li><a href="#" class="nav-link">فساتين سهرة</a></li>
            <li><a href="#" class="nav-link">فساتين كاجوال</a></li>
            <li><a href="#" class="nav-link">فساتين رسمية</a></li>
            <li><a href="#" class="nav-link">أحذية</a></li>
            <li><a href="#" class="nav-link">إكسسوارات</a></li>
            <li><a href="#" class="nav-link">تخفيضات</a></li>
        </ul>
    </div>
</nav>

<!-- Hero Section -->
<section class="shein-hero">
    <div class="container">
        <div class="hero-content">
            <h1>مجموعة فساتين السهرة الجديدة</h1>
            <p>اكتشفي أحدث صيحات الموضة في فساتين السهرة الأنيقة والعصرية بأسعار لا تقاوم</p>
            <a href="#products" class="shein-btn">تسوقي الآن</a>
        </div>
    </div>
</section>

<!-- Categories Section -->
<?php if (!empty($main_categories)): ?>
<section class="shein-categories">
    <div class="container">
        <h2 class="section-title">تسوقي حسب القسم</h2>

        <div class="categories-grid">
            <?php foreach ($main_categories as $cat): ?>
            <a href="shein-category.php?slug=<?php echo $cat['slug']; ?>" class="category-card">
                <div class="category-image">
                    <?php if ($cat['image']): ?>
                        <img src="uploads/categories/<?php echo htmlspecialchars($cat['image']); ?>" alt="<?php echo htmlspecialchars($cat['name']); ?>">
                    <?php else: ?>
                        <div class="no-image">
                            <i class="fas fa-tags"></i>
                        </div>
                    <?php endif; ?>
                    <div class="category-overlay">
                        <h3><?php echo htmlspecialchars($cat['name']); ?></h3>
                        <span class="shop-now">تسوقي الآن</span>
                    </div>
                </div>
            </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Featured Products -->
<?php if (!empty($featured_products)): ?>
<section id="products" class="shein-products">
    <div class="container">
        <h2 class="section-title">المنتجات المميزة</h2>
        
        <div class="products-grid">
            <?php foreach ($featured_products as $prod): ?>
            <div class="shein-product-card">
                <div class="product-image">
                    <?php if ($prod['featured_image']): ?>
                        <img src="<?php echo UPLOAD_URL . $prod['featured_image']; ?>" alt="<?php echo htmlspecialchars($prod['title']); ?>" loading="lazy">
                    <?php else: ?>
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                            <i class="fas fa-image" style="font-size: 48px;"></i>
                        </div>
                    <?php endif; ?>
                    
                    <div class="product-badges">
                        <?php if ($prod['is_featured']): ?>
                            <span class="badge new">جديد</span>
                        <?php endif; ?>
                        
                        <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                            <span class="badge sale">خصم</span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="product-info">
                    <h3 class="product-title">
                        <a href="product.php?slug=<?php echo $prod['slug']; ?>" style="text-decoration: none; color: inherit;">
                            <?php echo htmlspecialchars($prod['title']); ?>
                        </a>
                    </h3>
                    
                    <div class="product-price">
                        <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                            <span class="current-price"><?php echo format_price($prod['sale_price']); ?></span>
                            <span class="original-price"><?php echo format_price($prod['price']); ?></span>
                        <?php else: ?>
                            <span class="current-price"><?php echo format_price($prod['price']); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($prod['stock_status'] == 'in_stock'): ?>
                    <button class="add-to-cart" data-product-id="<?php echo $prod['id']; ?>">
                        أضيفي للسلة
                    </button>
                    <?php else: ?>
                    <button class="add-to-cart" disabled style="background: #ccc; cursor: not-allowed;">
                        نفذ من المخزن
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Latest Products -->
<?php if (!empty($latest_products)): ?>
<section class="shein-products" style="background: #fafafa;">
    <div class="container">
        <h2 class="section-title">أحدث المنتجات</h2>
        
        <div class="products-grid">
            <?php foreach ($latest_products as $prod): ?>
            <div class="shein-product-card">
                <div class="product-image">
                    <?php if ($prod['featured_image']): ?>
                        <img src="<?php echo UPLOAD_URL . $prod['featured_image']; ?>" alt="<?php echo htmlspecialchars($prod['title']); ?>" loading="lazy">
                    <?php else: ?>
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                            <i class="fas fa-image" style="font-size: 48px;"></i>
                        </div>
                    <?php endif; ?>
                    
                    <div class="product-badges">
                        <span class="badge new">جديد</span>
                        <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                            <span class="badge sale">خصم</span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="product-info">
                    <h3 class="product-title">
                        <a href="product.php?slug=<?php echo $prod['slug']; ?>" style="text-decoration: none; color: inherit;">
                            <?php echo htmlspecialchars($prod['title']); ?>
                        </a>
                    </h3>
                    
                    <div class="product-price">
                        <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                            <span class="current-price"><?php echo format_price($prod['sale_price']); ?></span>
                            <span class="original-price"><?php echo format_price($prod['price']); ?></span>
                        <?php else: ?>
                            <span class="current-price"><?php echo format_price($prod['price']); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($prod['stock_status'] == 'in_stock'): ?>
                    <button class="add-to-cart" data-product-id="<?php echo $prod['id']; ?>">
                        أضيفي للسلة
                    </button>
                    <?php else: ?>
                    <button class="add-to-cart" disabled style="background: #ccc; cursor: not-allowed;">
                        نفذ من المخزن
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Footer -->
<footer style="background: #000; color: #fff; padding: 40px 0; text-align: center;">
    <div class="container">
        <div style="margin-bottom: 20px;">
            <h3 style="font-size: 24px; font-weight: 900; margin-bottom: 10px;">SHEIN</h3>
            <p style="color: #ccc;">أجمل فساتين السهرة بأسعار لا تقاوم</p>
        </div>
        
        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <a href="#" style="color: #ccc; text-decoration: none;">من نحن</a>
            <a href="#" style="color: #ccc; text-decoration: none;">اتصل بنا</a>
            <a href="#" style="color: #ccc; text-decoration: none;">الشحن والإرجاع</a>
            <a href="#" style="color: #ccc; text-decoration: none;">الخصوصية</a>
        </div>
        
        <p style="color: #666; font-size: 12px;">© 2025 SHEIN Style. جميع الحقوق محفوظة.</p>
    </div>
</footer>

<script>
// Add to cart functionality
document.querySelectorAll('.add-to-cart').forEach(button => {
    button.addEventListener('click', function() {
        const productId = this.getAttribute('data-product-id');
        if (productId) {
            // Add your cart logic here
            this.textContent = 'تمت الإضافة!';
            this.style.background = '#2ed573';
            setTimeout(() => {
                this.textContent = 'أضيفي للسلة';
                this.style.background = '#000';
            }, 2000);
        }
    });
});
</script>

</body>
</html>
